import React from 'react';
import { Provider } from 'react-redux';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import AppNavigation from 'navigation/AppNavigation';
import { BottomSheetProvider } from 'context/BottomSheetModalContext';
import { GlobalContextProvider } from 'context/GlobalContext';
import store from 'store/store';
import logService from 'utils/logService';
import GlobalAlert from 'components/common/GlobalAlert';
import GlobalLoading from 'components/common/GlobalLoading';
import {useToken} from 'hooks/useToken';
import CodePush from '@revopush/react-native-code-push';
logService.init();

const TokenInitializer = () => {
  useToken();
  return null;
};

function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Provider store={store}>
        <SafeAreaProvider>
          <GlobalContextProvider>
            <BottomSheetProvider>
              {/* <TokenInitializer /> */}

              <AppNavigation />
              <GlobalAlert />
              <GlobalLoading />
            </BottomSheetProvider>
          </GlobalContextProvider>
        </SafeAreaProvider>
      </Provider>
    </GestureHandlerRootView>
  );
}

export default CodePush({
  checkFrequency: CodePush.CheckFrequency.MANUAL,
})(App);
