{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.1", "@logrocket/react-native": "^1.51.2", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-navigation/bottom-tabs": "^7.2.1", "@react-navigation/drawer": "^7.1.2", "@react-navigation/elements": "^2.2.6", "@react-navigation/native": "^7.0.15", "@react-navigation/native-stack": "^7.2.1", "@reduxjs/toolkit": "^2.6.0", "@revopush/react-native-code-push": "^1.1.0", "axios": "^1.8.1", "qs": "^6.14.0", "react": "19.0.0", "react-native": "0.78.0", "react-native-calendars": "^1.1310.0", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "^2.24.0", "react-native-modal": "^13.0.1", "react-native-permissions": "^5.4.0", "react-native-reanimated": "^3.17.1", "react-native-reanimated-table": "^0.0.2", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.9.1", "react-native-spinkit": "^1.5.1", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.13.5", "react-redux": "^9.2.0", "redux-logger": "^3.0.6", "redux-saga": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.0", "@react-native/eslint-config": "0.78.0", "@react-native/metro-config": "0.78.0", "@react-native/typescript-config": "0.78.0", "@types/jest": "^29.5.13", "@types/qs": "^6.9.18", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "@types/redux-logger": "^3.0.13", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}