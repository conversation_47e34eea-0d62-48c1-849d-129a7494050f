buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 25
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.4.2'
    }
}

allprojects {
    repositories {
        // Add this declaration to any existing repositories block. Do not remove any existing entries in the block.
        maven { url "https://storage.googleapis.com/logrocket-maven/" }
    }
}

apply plugin: "com.facebook.react.rootproject"
