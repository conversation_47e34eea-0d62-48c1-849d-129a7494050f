{
  "compilerOptions": {
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "jsx": "react",
    "lib": ["es6", "dom"],
    "moduleResolution": "node",
    "noEmit": true,
    "strict": true,
    "target": "esnext",
    "baseUrl": ".",
    "paths": {
      "*": ["src/*"],
      "store": ["src/store"],
      "components": ["src/components"],
      "api": ["src/api"],
      "utils": ["src/utils"],
      "screens": ["src/screens"],
      "navigation": ["src/navigation"],
      "assets": ["src/assets"]
    }
  },
  "exclude": [
    "node_modules",
    "babel.config.js",
    "metro.config.js",
    "jest.config.js"
  ],
  "include": ["index.js", "src", "global.d.ts", "react-native-col.d.ts", "env.d.ts"],
}
