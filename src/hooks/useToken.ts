import { useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { network } from 'api';

export const useToken = () => {
  useEffect(() => {
    const loadToken = async () => {
      try {
        const token = await AsyncStorage.getItem('ACCESS_TOKEN');
        if (token) {
          console.log('Token loaded from AsyncStorage:', token);
          network.setToken(token);
        } else {
          console.log('No token found in AsyncStorage');
        }
      } catch (error) {
        console.error('Error loading token from AsyncStorage:', error);
      }
    };

    loadToken();
  }, []);
};
