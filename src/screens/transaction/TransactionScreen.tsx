import { useIsFocused } from '@react-navigation/native';
import { colors, fonts, images } from 'assets';
import { <PERSON><PERSON>, WrapperContainer } from 'components';
import { useBottomSheet } from 'context/BottomSheetModalContext';
import { useGlobalContext } from 'context/GlobalContext';
import React, { useCallback, useEffect, useState } from 'react';
import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { screenFocusActions, transactionActions } from 'store';
import { scale } from 'utils/device';
import { convertDateYYMMDD, renderTransactionIcon } from 'utils/functions';
import Deposit from './components/Deposit';
import TransactionDetail from './components/TransactionDetail';
import Transfer from './components/Transfer';
import Withdraw from './components/Withdraw';

function TransactionScreen() {
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const { openBottomSheet, closeBottomSheet } = useBottomSheet();

  const { handleLoading } = useGlobalContext();

  const transaction = useSelector(
    (state: any) => state.transaction.getTransaction,
  );
  const deposit = useSelector((state: any) => state.transaction.getDeposit);
  const withdraw = useSelector((state: any) => state.transaction.getWithdraw);
  const transfer = useSelector((state: any) => state.transaction.getTransfer);
  const getDepositMethod = useSelector(
    (state: any) => state.transaction.getDepositMethod,
  );

  const [isShowLoadMore, setIsShowLoadMore] = useState(false);

  const [transactionTypeList, setTransactionTypeList] = useState([
    {
      title: 'All',
      value: 0,
    },
    {
      title: 'Deposit',
      value: 1,
    },
    {
      title: 'Withdraw',
      value: 2,
    },
    {
      title: 'Transfer',
      value: 3,
    },
  ]);

  const [selectedTransactionType, setSelectedTransactionType] = useState({
    title: 'ALl',
    value: 0,
  });

  const transactionListData = (type: any) => {
    switch (type?.value) {
      case 0:
        return transaction?.data;
      case 1:
        return deposit?.data;
      case 2:
        return withdraw?.data;
      case 3:
        return transfer?.data;
      default:
        return transaction?.data;
    }
  };

  const formatShowLoadMoreBtn = (type: any) => {
    switch (type?.value) {
      case 0:
        return transaction?.page < transaction?.last_page;
      case 1:
        return deposit?.page < deposit?.last_page;
      case 2:
        return withdraw?.page < withdraw?.last_page;
      case 3:
        return transfer?.page < transfer?.last_page;
      default:
        return transaction?.page < transaction?.last_page;
    }
  };

  useEffect(() => {
    if (isFocused) {
      dispatch(screenFocusActions.setCurrentScreen('TransactionScreen'));
    }
  }, [isFocused]);

  useEffect(() => {
    dispatch(transactionActions.getTransaction({ page: transaction.page }));
  }, [transaction.page]);

  useEffect(() => {
    dispatch(transactionActions.getDeposit({ page: deposit.page, type: 1 }));
  }, [deposit.page]);

  useEffect(() => {
    dispatch(transactionActions.getWithdraw({ page: withdraw.page, type: 2 }));
  }, [withdraw.page]);

  useEffect(() => {
    dispatch(transactionActions.getTransfer({ page: transfer.page, type: 3 }));
  }, [transfer.page]);

  useEffect(() => {
    setIsShowLoadMore(formatShowLoadMoreBtn(selectedTransactionType));
  }, [transaction, deposit, withdraw, transfer, selectedTransactionType]);



  // useEffect(() => {
  //   if (
  //     transaction?.loading ||
  //     deposit?.loading ||
  //     withdraw?.loading ||
  //     transfer?.loading ||
  //     getDepositMethod?.loading
  //   ) {
  //     handleLoading(true);
  //   } else {
  //     handleLoading(false);
  //   }
  // }, [
  //   transaction?.loading,
  //   deposit?.loading,
  //   withdraw?.loading,
  //   transfer?.loading,
  // ]);

  const onPressTransactionCode = item => {
    openBottomSheet({
      children: <TransactionDetail item={item} />,
      showCloseBtn: true,
    });
  };

  const renderTransactionItem = ({ item }) => (
    <View style={styles.transactionRow}>
      <Text style={styles.transactionDate}>
        {convertDateYYMMDD(item?.created_at)}
      </Text>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          width: scale(120),
          height: '100%',
        }}>
        <Image
          source={renderTransactionIcon(item)}
          style={styles.icTransactionType}
        />
        <Text style={styles.transactionInfo}>${item?.amount}</Text>
      </View>
      <TouchableOpacity
        onPress={() => onPressTransactionCode(item)}
        style={{ marginBottom: scale(1) }}>
        <Text style={styles.transactionCode}>{item?.transaction_code}</Text>
      </TouchableOpacity>
    </View>
  );

  const onPressLoadMoreBtn = () => {
    switch (selectedTransactionType?.value) {
      case 0:
        return dispatch(
          transactionActions.getTransactionUpdatePage(transaction.page + 1),
        );
      case 1:
        return dispatch(
          transactionActions.getDepositUpdatePage(deposit.page + 1),
        );
      case 2:
        return dispatch(
          transactionActions.getWithdrawUpdatePage(withdraw.page + 1),
        );
      case 3:
        return dispatch(
          transactionActions.getTransferUpdatePage(transfer.page + 1),
        );
    }
  };

  const renderLoadMoreBtn = () =>
    isShowLoadMore ? (
      <TouchableOpacity style={styles.loadMoreBtn} onPress={onPressLoadMoreBtn}>
        <Text
          style={{
            fontFamily: fonts.SFPro.medium,
            fontSize: scale(15),
            color: colors.white,
          }}>
          Load More
        </Text>
      </TouchableOpacity>
    ) : (
      <View style={{ height: scale(100) }} />
    );

  const renderTransactionList = useCallback(
    () => (
      <FlatList
        data={transactionListData(selectedTransactionType)}
        renderItem={renderTransactionItem}
        ListFooterComponent={renderLoadMoreBtn}
        showsVerticalScrollIndicator={false}
        bounces={false}
      />
    ),
    [selectedTransactionType, transactionListData],
  );

  const onPressDeposit = () => {
    openBottomSheet({
      children: (
        <Deposit
          openBottomSheet={openBottomSheet}
          closeBottomSheet={closeBottomSheet}
        />
      ),
      showCloseBtn: true,
    });
  };

  const onPressTransfer = () => {
    openBottomSheet({
      children: (
        <Transfer
          openBottomSheet={openBottomSheet}
          closeBottomSheet={closeBottomSheet}
        />
      ),
      showCloseBtn: true,
      showBackBtn: true,
    });
  };

  const onPressWithdraw = () => {
    openBottomSheet({
      children: (
        <Withdraw
          openBottomSheet={openBottomSheet}
          closeBottomSheet={closeBottomSheet}
        />
      ),
      showCloseBtn: true,
    });
  };

  const renderTransactionBtn = useCallback(
    () => (
      <View style={styles.transactionBtnWrapper}>
        <TouchableOpacity
          onPress={onPressDeposit}
          style={styles.transactionBtn}>
          <Image source={images.ic_deposit} style={styles.icon} />
          <Text style={[styles.transactionBtnTitle, { color: colors.yellow }]}>
            Deposit
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onPressTransfer}
          style={styles.transactionBtn}>
          <Image source={images.ic_transfer} style={styles.icon} />
          <Text style={[styles.transactionBtnTitle, { color: colors.white }]}>
            Transfer
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onPressWithdraw}
          style={styles.transactionBtn}>
          <Image source={images.ic_withdraw} style={styles.icon} />
          <Text style={[styles.transactionBtnTitle, { color: colors.yellow }]}>
            Withdraw
          </Text>
        </TouchableOpacity>
      </View>
    ),
    [],
  );

  return (
    <WrapperContainer style={{ backgroundColor: colors.black2 }}>
      <Header />
      <Text style={styles.screenTitle}>Transaction</Text>
      <View style={{ marginTop: scale(40), flex: 1 }}>
        <View style={{ flexDirection: 'row', paddingHorizontal: scale(20) }}>
          {transactionTypeList.map(item => (
            <TouchableOpacity
              key={item?.value}
              onPress={() => setSelectedTransactionType(item)}
              style={styles.transactionTypeBtn}>
              <Text
                style={[
                  styles.transactionTitle,
                  {
                    color:
                      item?.value === selectedTransactionType?.value
                        ? colors.yellow
                        : colors.white,
                  },
                ]}>
                {item?.title}
              </Text>
              {item?.value === selectedTransactionType?.value && (
                <View style={styles.dot} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.columnNameWrapper}>
          <Text style={[styles.columnName, { width: scale(120) }]}>
            Date Created
          </Text>
          <Text style={[styles.columnName, { width: scale(115) }]}>Amount</Text>
          <Text style={[styles.columnName]}>Code</Text>
        </View>
        {renderTransactionList()}
        {renderTransactionBtn()}
      </View>
    </WrapperContainer>
  );
}

export default TransactionScreen;

const styles = StyleSheet.create({
  screenTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(30),
    color: colors.white,
    paddingHorizontal: scale(20),
    marginTop: scale(35),
  },
  transactionTypeBtn: {
    paddingVertical: scale(10),
    marginRight: scale(30),
  },
  dot: {
    width: scale(5),
    height: scale(5),
    borderRadius: scale(5),
    backgroundColor: colors.yellow,
    alignSelf: 'center',
    marginTop: scale(3),
  },
  transactionTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
  },
  columnNameWrapper: {
    flexDirection: 'row',
    backgroundColor: 'black',
    paddingHorizontal: scale(20),
    paddingVertical: scale(12),
  },
  columnName: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(13),
    color: colors.white,
  },
  transactionInfo: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
  },
  icTransactionType: {
    width: scale(20),
    height: scale(20),
    marginRight: scale(7),
  },
  transactionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(20),
    paddingVertical: scale(10),
    borderBottomWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  transactionDate: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
    width: scale(120),
    height: '100%',
  },
  transactionCode: {
    fontFamily: fonts.SFPro.italic,
    color: colors.white,
    textDecorationLine: 'underline',
  },
  loadMoreBtn: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.black,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(5),
    marginTop: scale(20),
    marginBottom: scale(50),
  },
  transactionBtnWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'black',
    padding: scale(20),
  },
  transactionBtn: {
    backgroundColor: colors.black,
    width: scale(130),
    height: scale(75),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#1B1B1B',
    borderRadius: scale(10),
  },
  icon: {
    width: scale(24),
    height: scale(24),
  },
  transactionBtnTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    marginTop: scale(6),
  },
});
