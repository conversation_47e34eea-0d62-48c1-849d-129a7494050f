import {colors, fonts, images} from 'assets';
import {AlertSuccess} from 'components';
import React, {useState} from 'react';
import {
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {scale} from 'utils/device';

function Transfer({openBottomSheet, closeBottomSheet}) {
  const [amount, setAmount] = useState('');

  const onPressTransfer = () => {
    openBottomSheet({
      children: (
        <AlertSuccess title={'Transfer'} closeBottomSheet={closeBottomSheet} />
      ),
      showCloseBtn: true,
    });
  };

  return (
    <View>
      <Text style={styles.screenTitle}>Internal Transfer</Text>
      <Text style={[styles.inputLabel, {marginTop: scale(55)}]}>
        Source Account
      </Text>
      <TouchableOpacity activeOpacity={1}>
        <View style={styles.rowInput}>
          <Text style={styles.valueInput}>Wallet - 124851 - 549.00 USD </Text>
          <Image source={images.ic_arrow_down} style={styles.icDown} />
        </View>
      </TouchableOpacity>
      <Text style={[styles.inputLabel]}>Target Account</Text>
      <TouchableOpacity activeOpacity={1}>
        <View style={styles.rowInput}>
          <Text style={styles.valueInput}>Wallet - 124851 - 549.00 USD </Text>
          <Image source={images.ic_arrow_down} style={styles.icDown} />
        </View>
      </TouchableOpacity>
      <Text style={styles.inputLabel}>Amount</Text>
      <TouchableOpacity activeOpacity={1}>
        <View style={styles.rowInput}>
          <TextInput
            value={amount}
            style={styles.input}
            onChangeText={(text: string) => setAmount(text)}
          />
          <Text style={styles.usd}>USD</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity onPress={onPressTransfer} style={styles.transferBtn}>
        <Text style={styles.transferTxt}>Transfer</Text>
      </TouchableOpacity>
    </View>
  );
}

export default Transfer;

const styles = StyleSheet.create({
  screenTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(20),
    color: colors.white,
    marginTop: scale(97),
    marginLeft: scale(20),
  },
  inputLabel: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
    marginLeft: scale(20),
    marginTop: scale(20),
  },
  icDown: {
    width: scale(24),
    height: scale(12),
  },
  rowInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: colors.grayBorder,
    minHeight: scale(50),
    paddingHorizontal: scale(20),
  },
  valueInput: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    color: colors.yellow,
  },
  usd: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    color: colors.white,
  },
  input: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
    flex: 1,
    height: '100%',
  },
  transferBtn: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.yellow,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(5),
    alignSelf: 'center',
    marginTop: scale(87),
  },
  transferTxt: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.black,
  },
});
