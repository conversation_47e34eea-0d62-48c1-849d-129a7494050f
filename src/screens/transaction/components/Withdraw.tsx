import { colors, fonts, images } from 'assets';
import { AlertSuccess } from 'components';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Animated,
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import Spinner from 'react-native-spinkit';
import { useDispatch, useSelector } from 'react-redux';
import { transactionActions } from 'store';
import { scale } from 'utils/device';

function Withdraw({ openBottomSheet, closeBottomSheet }) {
  const { loading, data } = useSelector((state: any) => state.transaction.getWithdrawMethod)

  const dispatch = useDispatch();

  const translateList = useRef(new Animated.Value(0)).current;

  const translateInput = useRef(new Animated.Value(0)).current;

  const [selectedMethod, setSelectedMethod] = useState<any>({
    title: 'Bank Transfer',
    value: 'bank',
  });

  const [selectedBank, setSelectedBank] = useState<any>(null);

  const [note, setNote] = useState<string>('');

  const method = [
    {
      title: 'Bank Transfer',
      value: 'bank',
    },
  ];

  const onPressWithdraw = () => {
    openBottomSheet({
      children: (
        <AlertSuccess title={'Withdraw'} closeBottomSheet={closeBottomSheet} />
      ),
      showCloseBtn: true,
    });
  };


  const backToBankList = () => {
    Animated.parallel([
      Animated.timing(translateList, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      } as any),
      Animated.timing(translateInput, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      } as any),
    ]).start();
  };

  const showWithdrawInput = item => {
    Animated.parallel([
      Animated.timing(translateList, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      } as any),
      Animated.timing(translateInput, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      } as any),
    ]).start();
    setSelectedBank(item);
  };

  useEffect(() => {
    dispatch(transactionActions.getWithdrawMethod({ type: 3 }))
  }, [])

  const renderMethod = useCallback(
    () => (
      <View
        style={{
          flexDirection: 'row',
          paddingHorizontal: scale(20),
          marginTop: scale(40),
        }}>
        {method.map(item => (
          <TouchableOpacity
            onPress={() => setSelectedMethod(item)}
            style={styles.methodBtn}>
            <Text
              style={[
                styles.method,
                {
                  color:
                    item?.value === selectedMethod?.value
                      ? colors.yellow
                      : colors.white,
                },
              ]}>
              {item?.title}
            </Text>
            {item?.value === selectedMethod?.value && (
              <View style={styles.dot} />
            )}
          </TouchableOpacity>
        ))}
      </View>
    ),
    [selectedMethod?.value, method],
  );

  const renderList = useCallback(
    () => (
      <View style={{ marginTop: scale(26) }}>
        {data?.map((item: any, index: number) => (
          <TouchableOpacity onPress={() => showWithdrawInput(item)}>
            <View
              style={[
                styles.row,
                index === data.length - 1 && { borderBottomWidth: 1 },
              ]}>
              <View style={{ flex: 1, justifyContent: 'center' }}>
                <Text style={styles.bankName}>{item?.name}</Text>
                <View style={{ flexDirection: 'row' }}>
                  <Text style={styles.info}>Instant 24/7</Text>
                  <Text style={[styles.info, { marginLeft: scale(37) }]}>
                    Fee {item?.fee}%
                  </Text>
                </View>
              </View>
              <Image
                source={images.ic_arrow_right}
                style={{ width: scale(12), height: scale(24) }}
              />
            </View>
          </TouchableOpacity>
        ))}
      </View>
    ),
    [data],
  );

  const renderInputView = () => (
    <Animated.View
      style={{
        width: scale(440),
        paddingTop: scale(25),
        transform: [
          {
            translateX: translateInput.interpolate({
              inputRange: [0, 1],
              outputRange: [0, -scale(440)],
              extrapolate: 'clamp',
            }),
          },
        ],
      }}>
      <TouchableOpacity onPress={backToBankList} style={styles.backBtn}>
        <Image source={images.ic_back} style={styles.icBack} />
        <Text style={styles.backTxt}>Back</Text>
      </TouchableOpacity>
      <Text style={styles.bankNameTitle}>{selectedBank?.bankName}</Text>
      <Text style={[styles.inputLabel, { marginTop: scale(55) }]}>
        Trading Account
      </Text>
      <TouchableOpacity activeOpacity={1}>
        <View style={styles.rowInput}>
          <Text style={styles.valueInput}>
            {/* {accountType ? accountType?.label : ''} */}abc123
          </Text>
          <Image source={images.ic_arrow_down} style={styles.icDown} />
        </View>
      </TouchableOpacity>
      <Text style={styles.inputLabel}>Amount</Text>
      <TouchableOpacity activeOpacity={1}>
        <View style={styles.rowInput}>
          <Text style={styles.valueInput}>
            {/* {accountType ? accountType?.label : ''} */}abc123
          </Text>
          <Text style={styles.usd}>USD</Text>
        </View>
      </TouchableOpacity>
      <Text style={styles.inputLabel}>Amount Convert</Text>
      <TouchableOpacity activeOpacity={1}>
        <View style={styles.rowInput}>
          <Text style={styles.valueInput}>
            {/* {accountType ? accountType?.label : ''} */}abc123
          </Text>
          <Text style={styles.usd}>USD</Text>
        </View>
      </TouchableOpacity>
      <Text style={styles.inputLabel}>Choose Bank</Text>
      <TouchableOpacity activeOpacity={1}>
        <View style={styles.rowInput}>
          <Text style={styles.valueInput}>abc123</Text>
          <Image source={images.ic_arrow_down} style={styles.icDown} />
        </View>
      </TouchableOpacity>
      <Text style={styles.inputLabel}>Note</Text>
      <View style={styles.rowInput}>
        <TextInput
          value={note}
          onChangeText={(text: string) => setNote(text)}
          style={{
            width: '100%',
            height: '100%',
            fontFamily: fonts.SFPro.medium,
            fontSize: scale(15),
            color: colors.white,
          }}
        />
      </View>
      <TouchableOpacity
        style={{
          width: scale(100),
          height: scale(30),
          backgroundColor: colors.yellow,
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: scale(5),
          alignSelf: 'center',
          marginTop: scale(87),
        }}
        onPress={onPressWithdraw}>
        <Text>Withdraw</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <View style={{ width: scale(440), overflow: 'hidden', flexDirection: 'row' }}>
      <Animated.View
        style={[
          {
            width: scale(440),
            paddingTop: scale(18),
            transform: [
              {
                translateX: translateList.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -scale(440)],
                  extrapolate: 'clamp',
                }),
              },
            ],
          },
        ]}>
        <Text style={styles.screenTitle}>Withdraw</Text>
        {renderMethod()}
        {loading ? <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <Spinner
            isVisible={true}
            size={50}
            type={'FadingCircleAlt'}
            color={colors.white}
          />
        </View> : renderList()}
      </Animated.View>
      {renderInputView()}
    </View>
  );
}

export default Withdraw;

const styles = StyleSheet.create({
  container: {
    paddingTop: scale(18),
    flex: 1,
  },
  screenTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(30),
    color: colors.white,
    paddingHorizontal: scale(20),
  },
  methodBtn: {
    paddingVertical: scale(10),
    marginRight: scale(22),
  },
  method: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(20),
    color: colors.white,
  },
  dot: {
    width: scale(5),
    height: scale(5),
    borderRadius: scale(5),
    backgroundColor: colors.yellow,
    alignSelf: 'center',
    marginTop: scale(3),
  },
  row: {
    flexDirection: 'row',
    paddingTop: scale(20),
    paddingBottom: scale(25),
    paddingHorizontal: scale(20),
    alignItems: 'center',
    borderColor: '#28272C',
    borderTopWidth: 1,
  },
  bankName: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    color: colors.yellow,
  },
  info: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    color: colors.white,
    marginTop: scale(7),
  },
  backBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: scale(20),
  },
  icBack: {
    width: scale(24),
    height: scale(24),
  },
  backTxt: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    color: colors.white,
    marginLeft: scale(10),
  },
  bankNameTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(20),
    color: colors.white,
    marginTop: scale(52),
    paddingHorizontal: scale(20),
  },
  inputLabel: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
    marginLeft: scale(20),
    marginTop: scale(20),
  },
  icDown: {
    width: scale(24),
    height: scale(12),
  },
  rowInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: colors.grayBorder,
    minHeight: scale(50),
    paddingHorizontal: scale(20),
  },
  valueInput: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    color: colors.yellow,
  },
  usd: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    color: colors.white,
  },
});
