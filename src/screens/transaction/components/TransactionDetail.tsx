import Clipboard from '@react-native-clipboard/clipboard';
import {colors, fonts, images} from 'assets';
import React, {useEffect, useState} from 'react';
import {
  Image,
  NativeSyntheticEvent,
  StyleSheet,
  Text,
  TextLayoutEventData,
  TouchableOpacity,
  View,
} from 'react-native';
import {scale} from 'utils/device';
import {convertDateCreated} from 'utils/functions';

function TransactionDetail({item}) {
  const [data, setData] = useState<any>([]);

  const statusTransaction = [
    {
      name: 'New',
      code: 1,
    },
    {
      name: 'Pending',
      code: 2,
    },
    {
      name: 'Approved',
      code: 3,
    },
    {
      name: 'Cancelled',
      code: 4,
    },
  ];

  const transactionStatus = statusTransaction.find(
    (i: any) => i.code === item?.status,
  );

  const detailData = [
    {
      key: 'Transaction Code',
      value: item?.transaction_code,
      isCopy: true,
    },
    {
      key: 'Method',
      value: item?.method?.name,
    },
    {
      key: 'Bank Name',
      value: item?.bankaccount?.name,
    },
    {
      key: 'Beneficiary Name',
      value: item?.bankaccount?.holder_name,
      isCopy: true,
    },
    {
      key: 'Account Number',
      value: item?.bankaccount?.account,
      isCopy: true,
    },
    {
      key: 'Bank Code (SWIFT/BIC)',
      value: item?.bankaccount?.swift_code,
      isCopy: true,
    },
    {
      key: 'Bank Address',
      value: item?.bankaccount?.branch,
    },
    {
      key: 'Bank Country',
      value: item?.bankaccount?.country,
    },
    {
      key: item?.type === 1 ? 'Deposit Amount USD' : 'Withdraw Amount USD',
      value: `${item?.amount} ${item?.currency}`,
    },
    {
      key: 'Receive Amount',
      value: `${item?.amount1} ${item?.currency1}`,
    },
    {
      key: item?.type === 1 ? 'Deposit Fee' : 'Withdraw Fee',
      value: `${item?.fee} ${item?.currency}`,
    },
    {
      key: 'Status',
      value: transactionStatus?.name,
    },
  ];
  // const detailDataWithdraw = [
  //   {
  //     key: 'Transaction Code',
  //     value: item?.transaction_code,
  //     isCopy: true,
  //   },
  //   {
  //     key: 'Method',
  //     value: item?.method?.name,
  //   },
  //   {
  //     key: 'Withdraw Amount USD',
  //     value: `${item?.amount} ${item?.currency}`,
  //   },
  //   {
  //     key: 'Address to Send',
  //     value: `${item?.address}`,
  //     isCopy: true,
  //   },
  // ];

  const detailDataTransfer = [
    {
      key: 'Transaction Code',
      value: item?.transaction_code,
      isCopy: true,
    },
    {
      key: 'Created At',
      value: convertDateCreated(item?.created_at),
    },
    {
      key: 'Source Account',
      value: item?.account?.login,
    },
    {
      key: 'Target Account',
      value: item?.accountto?.login,
    },
    {
      key: 'Amount',
      value: `${item?.amount} ${item?.currency}`,
    },
    {
      key: 'Status',
      value: transactionStatus?.name,
    },
  ];

  useEffect(() => {
    if (item?.type === 1 || item?.type === 2) {
      setData(detailData);
    } else if (item?.type === 3) {
      setData(detailDataTransfer);
    }
  }, [item?.type]);

  const [lineHeights, setLineHeights] = useState({});

  const handleTextLayout = (
    index: number,
    event: NativeSyntheticEvent<TextLayoutEventData>,
  ) => {
    const numberOfLines = event.nativeEvent.lines.length;
    setLineHeights(prev => ({
      ...prev,
      [index]: numberOfLines > 1 ? scale(20) : scale(40),
    }));
  };

  return (
    <View style={styles.container}>
      <Text style={styles.code}>{item?.transaction_code}</Text>
      <View style={{marginTop: scale(45)}}>
        {data?.map((data: any, index: number) => (
          <View key={data?.key} style={styles.row}>
            <Text style={styles.key}>{data?.key}</Text>
            <View
              style={{
                width: scale(178),
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-end',
              }}>
              <Text
                numberOfLines={1}
                style={[
                  styles.value,
                  {lineHeight: lineHeights[index] || scale(40)},
                ]}
                onTextLayout={event => handleTextLayout(index, event)}>
                {data?.value}
              </Text>
              {data?.isCopy && data?.value && (
                <TouchableOpacity
                  style={{padding: scale(6)}}
                  onPress={() => {
                    Clipboard.setString(data?.value);
                  }}>
                  <Image source={images.ic_copy} style={styles.icCopy} />
                </TouchableOpacity>
              )}
            </View>
          </View>
        ))}
      </View>
    </View>
  );
}

export default TransactionDetail;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: scale(20),
    paddingTop: scale(25),
  },
  code: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(20),
    color: colors.white,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  key: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    lineHeight: scale(40),
    color: colors.white,
  },
  value: {
    textAlign: 'right',
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
    lineHeight: scale(40),
  },
  icCopy: {
    width: scale(16),
    height: scale(16),
    tintColor: colors.white,
  },
});
