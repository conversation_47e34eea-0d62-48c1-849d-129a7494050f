import AsyncStorage from '@react-native-async-storage/async-storage';
import {CommonActions, useNavigation} from '@react-navigation/native';
import CodePush from '@revopush/react-native-code-push';
import {network} from 'api';
import {colors, fonts, images} from 'assets';
import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {scale} from 'utils/device';
import {updateAppWithCodepush} from 'utils/functions';

export default function SplashScreen() {
  const navigation = useNavigation();
  const [progress, setProgress] = useState(0);
  const appVersion = DeviceInfo.getVersion();
  const [updateMetadata, setUpdateMetadata] = useState<any>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    checkUpdateApp();
  }, []);

  const checkToken = async () => {
    const isFirst = await AsyncStorage.getItem('isFirst');
    if (isFirst) {
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{name: 'Welcome'}],
        }),
      );
    } else {
      const token = await AsyncStorage.getItem('ACCESS_TOKEN');
      if (token) {
        // AsyncStorage.removeItem('ACCESS_TOKEN');
        network.setToken(token);
        navigation.dispatch(
          CommonActions.reset({
            routes: [{name: 'MainStack'}],
          }),
        );
      } else {
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{name: 'Login'}],
          }),
        );
      }
    }
  };

  const checkUpdateApp = async () => {
    try {
      await updateAppWithCodepush(
        (percentComplete: any) => {
          setProgress(percentComplete);
        },
        () => {
          setIsUpdating(true);
          setProgress(0);
        },
      );
      const metadata: any = await CodePush.getUpdateMetadata();
      if (metadata) {
        setUpdateMetadata(metadata);
      }
      checkToken();
    } catch (error) {
      console.log('CHECK ERROR CODEPUSH', error);
      checkToken();
    }
  };

  return (
    <View style={styles.container}>
      <Image
        source={images.logo_tiger}
        style={{width: scale(125), height: scale(125)}}
      />
      <View style={{position: 'absolute', alignItems: 'center', bottom: 40}}>
        {isUpdating && (
          <View
            style={{
              height: scale(10),
              width: scale(200),
              borderWidth: 1,
              borderColor: colors.white,
              borderRadius: scale(20),
              marginTop: scale(25),
              justifyContent: 'center',
            }}>
            <View
              style={{
                height: scale(8),
                backgroundColor: colors.white,
                width: `${progress}%`,
                borderRadius: scale(20),
              }}
            />
          </View>
        )}
        <Text style={styles.txtVersion}>
          {'Version ' +
            appVersion +
            (updateMetadata?.label ? updateMetadata?.label : '')}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0F0F14',
    alignItems: 'center',
    justifyContent: 'center',
  },
  txtVersion: {
    fontSize: scale(12),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    marginTop: 8,
  },
});
