import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import React from 'react';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';

interface Props {
  onClose: () => void;
}

const AccountSuccess: React.FC<Props> = ({onClose}) => {
  return (
    <View style={styles.container}>
      <Image
        source={images.ic_account_create_success}
        style={styles.successIcon}
      />
      <Text style={styles.title}>Create Account</Text>
      <Text style={styles.subtitle}>Successfull</Text>

      <TouchableOpacity style={styles.goBackButton} onPress={onClose}>
        <Text style={styles.buttonText}>Go Back</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: scale(20),
  },
  successIcon: {
    width: scale(90),
    height: scale(90),
    marginBottom: scale(30),
  },
  title: {
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: scale(35),
    fontFamily: fonts.SFPro.medium,
    color: colors.yellow,
    textAlign: 'center',
    marginTop: scale(5),
  },
  goBackButton: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.yellow,
    borderRadius: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: scale(40),
  },
  buttonText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.black,
  },
});

export default AccountSuccess;
