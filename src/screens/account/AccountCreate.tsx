import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import React, {useState} from 'react';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';

import {Dropdown} from 'components';

interface Props {
  onSuccess: () => void;
}

const AccountCreate: React.FC<Props> = ({onSuccess}) => {
  const [tradingGroup, setTradingGroup] = useState('SmileU');
  const [leverage, setLeverage] = useState('1:100');
  const [password, setPassword] = useState('');

  const tradingGroups = [
    {id: '1', name: '<PERSON><PERSON>'},
    {id: '2', name: 'Group A'},
    {id: '3', name: 'Group B'},
  ];

  const leverageOptions = [
    {id: '1', name: '1:100'},
    {id: '2', name: '1:200'},
    {id: '3', name: '1:500'},
    {id: '4', name: '1:1000'},
  ];

  const changePassword = (text: string) => {
    setPassword(text);
  };

  const handleCreate = () => {
    onSuccess();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Create New Live Account</Text>

      <View style={styles.inputContainer}>
        <View style={styles.inputSection}>
          <Text style={styles.label}>Trading Group</Text>
          <Dropdown
            data={tradingGroups}
            selectedValue={tradingGroup}
            onSelect={setTradingGroup}
            zIndex={2000}
          />
        </View>

        <View style={styles.inputSection}>
          <Text style={styles.label}>Leverage</Text>
          <Dropdown
            data={leverageOptions}
            selectedValue={leverage}
            onSelect={setLeverage}
            zIndex={1000}
          />
        </View>

        <View style={styles.inputSection}>
          <Text style={styles.label}>Enter Password</Text>
          <TextInput
            value={password}
            style={styles.input}
            onChangeText={changePassword}
            placeholder="Enter Password"
            placeholderTextColor={colors.gray_border}
            secureTextEntry={true}
          />
        </View>
      </View>

      <TouchableOpacity style={styles.createButton} onPress={handleCreate}>
        <Text style={styles.createButtonText}>Create</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: scale(100),
  },
  title: {
    fontSize: scale(20),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginBottom: scale(55),
    paddingHorizontal: scale(20),
  },
  inputContainer: {
    gap: scale(16),
  },
  inputSection: {
    height: scale(70),
    borderBottomWidth: 0.2,
    borderColor: colors.gray_border,
    paddingHorizontal: scale(20),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginBottom: scale(16),
  },
  separator: {
    height: 1,
    backgroundColor: colors.gray_border,
  },
  input: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
  createButton: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.yellow,
    borderRadius: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    position: 'absolute',
    bottom: scale(40),
  },
  createButtonText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.black,
  },
});

export default AccountCreate;
