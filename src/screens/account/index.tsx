import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import React, {useState} from 'react';
import {Head<PERSON>, WrapperContainer} from 'components';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import {useGlobalContext} from 'context/GlobalContext';
import AccountCreate from './AccountCreate';
import AccountSuccess from './AccountSuccess';
import AccountDetail from './AccountDetail';

interface Props {}

const AccountScreen: React.FC<Props> = () => {
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const {handleLoading} = useGlobalContext();
  const [activeTab, setActiveTab] = useState(0);
  const accountTypes = ['Wallet Account', 'Live Account', 'Demo Account'];

  const accounts = [
    {
      balance: '12,8.50 USD',
      type: 'Wallet Account',
      id: '***********',
      company: 'SandaiinvestmentCo-Real',
      date: '2021-02-10 10:35:30',
      leverage: '1:5000',
    },
    {
      balance: '102,8.50 USD',
      type: 'Wallet Account',
      id: '***********',
      company: 'SandaiinvestmentCo-Real',
      date: '2021-02-10 10:35:30',
      leverage: '1:5000',
    },
    {
      balance: '902,8.50 USD',
      type: 'Wallet Account',
      id: '***********',
      company: 'SandaiinvestmentCo-Real',
      date: '2021-02-10 10:35:30',
      leverage: '1:5000',
    },
  ];

  const handleCreateAccount = () => {
    openBottomSheet({
      children: (
        <AccountCreate
          onSuccess={() => {
            handleLoading(true);
            setTimeout(() => {
              handleSuccess();
            }, 1000);
          }}
        />
      ),
      showBackBtn: true,
      showCloseBtn: false,
      enablePanDownToClose: true,
    });
  };

  const handleSuccess = () => {
    handleLoading(false);
    openBottomSheet({
      children: (
        <AccountSuccess
          onClose={() => {
            closeBottomSheet();
          }}
        />
      ),
      showBackBtn: false,
      showCloseBtn: true,
      enablePanDownToClose: true,
    });
  };

  const handleAccountPress = (account: any) => {
    openBottomSheet({
      children: <AccountDetail account={account} />,
      showCloseBtn: true,
      enablePanDownToClose: true,
    });
  };

  return (
    <WrapperContainer style={styles.wrapper}>
      <Header />
      <View style={styles.container}>
        <Text style={styles.title}>Account</Text>

        <View style={styles.tabContainer}>
          {accountTypes.map((type, index) => (
            <TouchableOpacity
              key={index}
              style={styles.tabItem}
              onPress={() => setActiveTab(index)}>
              {index === activeTab && <View style={styles.activeDot} />}
              <Text style={styles.tabText}>{type}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {accounts.map((account, index) => (
          <TouchableOpacity
            key={index}
            style={styles.accountCard}
            onPress={() => handleAccountPress(account)}>
            <Text style={styles.balanceText}>{account.balance}</Text>
            <Text style={styles.accountType}>{account.type}</Text>

            <View style={styles.accountDetails}>
              <View style={styles.detailRow}>
                <View style={styles.leftDetail}>
                  <Text style={styles.detailText}>ID#{account.id}</Text>
                </View>
                <View style={styles.rightDetail}>
                  <Text style={styles.detailText}>{account.company}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.leftDetail}>
                  <Text style={styles.detailText}>{account.date}</Text>
                </View>
                <View style={styles.rightDetail}>
                  <Text style={styles.detailText}>
                    LEVERAGE {account.leverage}
                  </Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          onPress={handleCreateAccount}
          style={styles.createButton}>
          <Text style={styles.createButtonText}>Create New Demo Account</Text>
        </TouchableOpacity>
      </View>
    </WrapperContainer>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: colors.background,
  },
  container: {
    flex: 1,
    paddingHorizontal: scale(16),
    paddingTop: scale(34),
  },
  title: {
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginBottom: scale(40),
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: scale(20),
    gap: scale(16),
  },
  tabItem: {
    position: 'relative',
  },
  activeDot: {
    width: scale(5),
    height: scale(5),
    backgroundColor: colors.yellow,
    borderRadius: scale(5),
    position: 'absolute',
    left: scale(51),
    top: scale(23),
  },
  tabText: {
    color: colors.white,
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
  },
  accountCard: {
    backgroundColor: colors.black,
    borderRadius: scale(10),
    padding: scale(16),
    marginBottom: scale(10),
  },
  balanceText: {
    color: colors.white,
    fontSize: scale(24),
    fontFamily: fonts.SFPro.medium,
    marginBottom: scale(4),
  },
  accountType: {
    color: colors.yellow,
    fontSize: scale(13),
    fontFamily: fonts.SFPro.medium,
    marginBottom: scale(12),
  },
  accountDetails: {
    marginTop: scale(4),
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: scale(4),
  },
  leftDetail: {
    flex: 1,
  },
  rightDetail: {
    flex: 1,
    alignItems: 'flex-start',
  },
  detailText: {
    color: colors.white,
    fontSize: scale(13),
    fontFamily: fonts.SFPro.medium,
    fontStyle: 'italic',
  },
  buttonContainer: {
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: scale(20),
  },
  createButton: {
    backgroundColor: colors.yellow,
    borderRadius: scale(5),
    height: scale(30),
    justifyContent: 'center',
    alignItems: 'center',
    width: scale(205),
    marginVertical: scale(15),
  },
  createButtonText: {
    color: colors.black,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
});

export default AccountScreen;
