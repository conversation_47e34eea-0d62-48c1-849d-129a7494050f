import {View, Text, StyleSheet} from 'react-native';
import React from 'react';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';

interface AccountDetailProps {
  account: {
    balance: string;
    type: string;
    id: string;
    company: string;
    date: string;
    leverage: string;
  };
}

const AccountDetail: React.FC<AccountDetailProps> = ({account}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.balance}>{account.balance}</Text>
      <Text style={styles.accountType}>{account.type}</Text>

      <View style={styles.detailsContainer}>
        <View style={styles.detailRow}>
          <Text style={styles.label}>Account ID</Text>
          <Text style={styles.value}>{account.id}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.label}>Server</Text>
          <Text style={styles.value}>{account.company}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.label}>Leverage</Text>
          <Text style={styles.value}>{account.leverage}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.label}>Date Created</Text>
          <Text style={styles.value}>{account.date}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: scale(20),
    paddingTop: scale(25),
  },
  balance: {
    color: colors.white,
    fontSize: scale(40),
    fontFamily: fonts.SFPro.medium,
    textTransform: 'uppercase',
  },
  accountType: {
    color: colors.yellow,
    fontSize: scale(13),
    fontFamily: fonts.SFPro.medium,
    marginTop: scale(8),
    marginBottom: scale(45),
  },
  detailsContainer: {
    gap: scale(20),
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    flex: 1,
  },
  value: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    flex: 1,
    textAlign: 'right',
  },
});

export default AccountDetail;
