import {useIsFocused, useNavigation} from '@react-navigation/native';
import {colors, fonts, images} from 'assets';
import {AlertSuc<PERSON>, Header, WrapperContainer} from 'components';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import {useGlobalContext} from 'context/GlobalContext';
import React, {useEffect, useRef, useState} from 'react';
import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {contactActions, screenFocusActions} from 'store';
import {scale} from 'utils/device';
import {validateEmail} from 'utils/functions';

function ContactScreen() {
  const navigation = useNavigation<any>();
  const isFocused = useIsFocused();
  const dispatch = useDispatch();
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const {handleLoading, showMessage} = useGlobalContext();

  const nameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const titleInputRef = useRef<TextInput>(null);
  const messageInputRef = useRef<TextInput>(null);

  const [content, setContent] = useState<string>('');
  const [fullName, setFullName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [title, setTitle] = useState<string>('');

  const {loading, data, message} = useSelector(
    (state: any) => state.contact.sendContact,
  );

  useEffect(() => {
    if (isFocused) {
      dispatch(screenFocusActions.setCurrentScreen('ContactScreen'));
    }
  }, [isFocused]);

  const onPressSend = () => {
    if (!fullName || !email || !title || !content) {
      return showMessage({
        message: 'Please fill all fields',
        type: 'error',
      });
    }
    if (!validateEmail(email)) {
      return showMessage({
        message: 'Invalid email address',
        type: 'error',
      });
    }
    dispatch(
      contactActions.sendContact({
        name: fullName,
        email,
        title,
        message: content,
      }),
    );
  };

  useEffect(() => {
    handleLoading(loading);
  }, [loading]);

  useEffect(() => {
    if (message) {
      setTimeout(() => {
        openBottomSheet({
          children: (
            <AlertSuccess
              message={message}
              closeBottomSheet={closeBottomSheet}
            />
          ),
          showCloseBtn: true,
        });
      }, 500);
      setFullName('');
      setEmail('');
      setTitle('');
      setContent('');
    }
  }, [message]);

  return (
    <WrapperContainer style={{backgroundColor: colors.background}}>
      <Header />
      <View style={styles.titleRow}>
        <Text style={styles.screenTitle}>Contact</Text>
      </View>
      <ScrollView bounces={false} contentContainerStyle={styles.scrollView}>
        <View style={[styles.wrapperInfo, {paddingTop: scale(9)}]}>
          <Text style={styles.title}>Information</Text>
          <View style={[styles.row, {alignItems: 'flex-start'}]}>
            <Image source={images.ic_address} style={styles.icon} />
            <Text style={styles.info}>
              Exchange Square, 14th Fl, St 106, Sangkat Wat Phnom, Khan Daun
              Penh, Phnom Penh, Cambodia
            </Text>
          </View>
          <View style={styles.row}>
            <Image source={images.ic_email} style={styles.icon} />
            <Text style={styles.info}><EMAIL></Text>
          </View>
          <View style={styles.row}>
            <Image source={images.ic_phone} style={styles.icon} />
            <Text style={styles.info}>0984512120</Text>
          </View>
        </View>
        <View
          onTouchEnd={() => nameInputRef.current?.focus()}
          style={styles.wrapperInfo}>
          <Text style={styles.title}>Your Fullname</Text>
          <TextInput
            ref={nameInputRef}
            value={fullName}
            placeholder="Enter your name"
            placeholderTextColor={`${colors.white}70`}
            autoCapitalize="none"
            onChangeText={(text: string) => setFullName(text)}
            style={styles.input}
            multiline
            selectionColor={colors.white}
          />
        </View>
        <View
          onTouchEnd={() => emailInputRef.current?.focus()}
          style={styles.wrapperInfo}>
          <Text style={styles.title}>Your Email</Text>
          <TextInput
            ref={emailInputRef}
            value={email}
            placeholder="Enter your email"
            placeholderTextColor={`${colors.white}70`}
            keyboardType="email-address"
            autoCapitalize="none"
            onChangeText={(text: string) => setEmail(text)}
            style={styles.input}
            multiline
            selectionColor={colors.white}
          />
        </View>
        <View
          onTouchEnd={() => titleInputRef.current?.focus()}
          style={styles.wrapperInfo}>
          <Text style={styles.title}>Your Title</Text>
          <TextInput
            ref={titleInputRef}
            value={title}
            placeholder="Enter title"
            placeholderTextColor={`${colors.white}70`}
            autoCapitalize="none"
            onChangeText={(text: string) => setTitle(text)}
            style={styles.input}
            multiline
            selectionColor={colors.white}
          />
        </View>
        <View
          onTouchEnd={() => messageInputRef.current?.focus()}
          style={[styles.wrapperInfo, {borderBottomWidth: 1}]}>
          <Text style={styles.title}>Your Message</Text>
          <TextInput
            ref={messageInputRef}
            value={content}
            placeholder="Enter message"
            placeholderTextColor={`${colors.white}70`}
            autoCapitalize="none"
            onChangeText={(text: string) => setContent(text)}
            style={styles.input}
            multiline
            selectionColor={colors.white}
          />
        </View>
        <TouchableOpacity onPress={onPressSend} style={styles.sendBtn}>
          <Text style={styles.sendTxt}>Send</Text>
        </TouchableOpacity>
      </ScrollView>
    </WrapperContainer>
  );
}

export default ContactScreen;

const styles = StyleSheet.create({
  scrollView: {
    paddingBottom: scale(20),
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: scale(35),
    justifyContent: 'space-between',
    paddingHorizontal: scale(20),
    marginBottom: scale(25),
  },
  screenTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(30),
    color: colors.white,
  },
  row: {
    flexDirection: 'row',
    marginTop: scale(30),
    alignItems: 'center',
  },
  icon: {
    width: scale(20),
    height: scale(20),
  },
  title: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
  },
  info: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    color: colors.white,
    paddingLeft: scale(10),
  },
  wrapperInfo: {
    paddingHorizontal: scale(20),
    borderColor: `${colors.white}10`,
    borderTopWidth: 1,
    paddingVertical: scale(20),
  },
  input: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    marginTop: scale(20),
    color: colors.white,
  },
  sendBtn: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.yellow,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(5),
    alignSelf: 'center',
    marginTop: scale(30),
  },
  sendTxt: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.black,
  },
});
