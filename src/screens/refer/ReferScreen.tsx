import React, {useState, useMemo, useCallback, memo, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import {<PERSON><PERSON>, WrapperContainer} from 'components';
import {fonts, images} from 'assets';
import {colors} from 'assets';
import {scale} from 'utils/device';
import {
  MyClients,
  MIBClients,
  MyIBAndMIB,
  IBsUnder,
  IBRequest,
  RequestIB,
} from './components';
import {Button} from './shared';
import {SuccessScreen} from '../profile/shared';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import {useDispatch} from 'react-redux';
import {screenFocusActions} from 'store';
import {referActions} from 'store/reducers/refer/actions';
import {useIsFocused} from '@react-navigation/native';

interface Props {}

interface TabItemProps {
  label: string;
  isActive: boolean;
  onPress: () => void;
}

const TabItem: React.FC<TabItemProps> = memo(({label, isActive, onPress}) => (
  <TouchableOpacity
    activeOpacity={0.8}
    style={styles.tabItem}
    onPress={onPress}>
    <Text style={styles.tabText}>{label}</Text>
    {isActive && <View style={styles.activeDot} />}
  </TouchableOpacity>
));

const ReferScreen: React.FC<Props> = () => {
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    if (isFocused) {
      dispatch(screenFocusActions.setCurrentScreen('ReferScreen'));
    }
  }, [isFocused, dispatch]);

  const tabItems = useMemo(
    () => ['My Clients', 'MIB Clients', 'My IB&MIB', 'IBs Under', 'IB Request'],
    [],
  );

  const handleTabPress = useCallback(
    (index: number) => () => setActiveTab(index),
    [],
  );

  const renderTabHeader = useMemo(() => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tabContainer}>
        {tabItems.map((item, index) => (
          <TabItem
            key={item}
            label={item}
            isActive={index === activeTab}
            onPress={handleTabPress(index)}
          />
        ))}
      </ScrollView>
    );
  }, [activeTab, tabItems, handleTabPress]);

  const renderContent = useMemo(() => {
    switch (activeTab) {
      case 0:
        return <MyClients />;
      case 1:
        return <MIBClients />;
      case 2:
        return <MyIBAndMIB />;
      case 3:
        return <IBsUnder />;
      case 4:
        return <IBRequest />;
      default:
        return null;
    }
  }, [activeTab]);

  const handleRequestSuccess = useCallback(() => {
    closeBottomSheet();

    setTimeout(() => {
      openBottomSheet({
        children: (
          <SuccessScreen
            onClose={() => {
              closeBottomSheet();
              // Refresh IB request data when user clicks "Go Back"
              if (activeTab === 4) { // 4 is the index of "IB Request" tab
                dispatch(referActions.getIBRequest({page: 1, limit: 10}));
              }
            }}
            title="Request IB"
            subtitle="Successful"
            buttonTitle="Go Back"
          />
        ),
        showCloseBtn: true,
        enablePanDownToClose: true,
      });
    }, 800);
  }, [openBottomSheet, closeBottomSheet, dispatch, activeTab]);

  const handleRequestIB = useCallback(() => {
    openBottomSheet({
      children: <RequestIB onBack={closeBottomSheet} onSuccess={handleRequestSuccess} />,
      showBackBtn: false,
      enablePanDownToClose: true,
    });
  }, [openBottomSheet, closeBottomSheet, handleRequestSuccess]);

  const renderRequestButton = useMemo(
    () => (
      <View style={styles.buttonContainer}>
        <Button title="Request New IB" onPress={handleRequestIB} />
      </View>
    ),
    [handleRequestIB],
  );

  return (
    <WrapperContainer style={{backgroundColor: colors.black2}}>
      <Header />
      <View style={styles.container}>
        <View style={styles.headerContainer}>
          <Text style={styles.title}>Refer</Text>
          <TouchableOpacity style={styles.searchButton}>
            <Image source={images.ic_search} style={styles.searchIcon} />
          </TouchableOpacity>
        </View>
        {renderTabHeader}
      </View>
      <ScrollView
        style={styles.contentContainer}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>{renderContent}</View>
      </ScrollView>
      {renderRequestButton}
    </WrapperContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: scale(16),
    paddingTop: scale(34),
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: scale(40),
  },
  title: {
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  searchButton: {
    padding: scale(5),
  },
  searchIcon: {
    width: scale(24),
    height: scale(24),
  },
  tabContainer: {
    gap: scale(16),
    paddingBottom: scale(20),
  },
  tabItem: {
    position: 'relative',
    alignItems: 'center',
  },
  activeDot: {
    width: scale(5),
    height: scale(5),
    backgroundColor: colors.yellow,
    borderRadius: scale(5),
    position: 'absolute',
    bottom: scale(-8),
  },
  tabText: {
    color: colors.white,
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
  },
  contentContainer: {},
  content: {
    gap: scale(24),
  },
  buttonContainer: {
    marginBottom: scale(20),
  },
});
export default memo(ReferScreen);
