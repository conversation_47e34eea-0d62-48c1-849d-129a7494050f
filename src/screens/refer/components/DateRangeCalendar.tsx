import React, {useState, useMemo, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';

interface DateRangeCalendarProps {
  onClose?: () => void;
  onDateRangeSelect?: (startDate: Date, endDate: Date) => void;
}

const DateRangeCalendar: React.FC<DateRangeCalendarProps> = ({
  onClose,
  onDateRangeSelect,
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedStartDate, setSelectedStartDate] = useState<Date | null>(null);
  const [selectedEndDate, setSelectedEndDate] = useState<Date | null>(null);

  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const dayNames = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];

  const getMonthData = useCallback((date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);

    // Get the day of week for first day (0 = Sunday, 1 = Monday, etc.)
    // Convert to Monday = 0, Sunday = 6
    let firstDayOfWeek = firstDay.getDay();
    firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

    const daysInMonth = lastDay.getDate();
    const weeks = [];

    let currentWeek = [];

    // Add empty cells for days before the first day of month
    for (let i = 0; i < firstDayOfWeek; i++) {
      const prevMonthDate = new Date(year, month, -(firstDayOfWeek - 1 - i));
      currentWeek.push({
        date: prevMonthDate,
        isCurrentMonth: false,
        weekNumber: null,
      });
    }

    // Add days of current month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);

      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }

      currentWeek.push({
        date,
        isCurrentMonth: true,
        weekNumber: weeks.length + 1,
      });
    }

    // Add empty cells for days after the last day of month
    while (currentWeek.length < 7) {
      const nextMonthDate = new Date(
        year,
        month + 1,
        currentWeek.length - firstDayOfWeek - daysInMonth + 1,
      );
      currentWeek.push({
        date: nextMonthDate,
        isCurrentMonth: false,
        weekNumber: null,
      });
    }

    if (currentWeek.length > 0) {
      weeks.push(currentWeek);
    }

    return weeks;
  }, []);

  const handleDatePress = useCallback(
    (date: Date) => {
      if (!selectedStartDate || (selectedStartDate && selectedEndDate)) {
        // Start new selection
        setSelectedStartDate(date);
        setSelectedEndDate(null);
      } else {
        // Select end date
        if (date < selectedStartDate) {
          // If selected date is before start date, make it the new start date
          setSelectedEndDate(selectedStartDate);
          setSelectedStartDate(date);
        } else {
          setSelectedEndDate(date);
        }
      }
    },
    [selectedStartDate, selectedEndDate],
  );

  const isDateInRange = useCallback(
    (date: Date) => {
      if (!selectedStartDate) return false;
      if (!selectedEndDate)
        return date.getTime() === selectedStartDate.getTime();

      return date >= selectedStartDate && date <= selectedEndDate;
    },
    [selectedStartDate, selectedEndDate],
  );

  const isDateSelected = useCallback(
    (date: Date) => {
      if (!selectedStartDate) return false;

      return (
        date.getTime() === selectedStartDate.getTime() ||
        (selectedEndDate && date.getTime() === selectedEndDate.getTime())
      );
    },
    [selectedStartDate, selectedEndDate],
  );

  const navigateMonth = useCallback((direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  }, []);

  const handleApply = useCallback(() => {
    if (selectedStartDate && selectedEndDate && onDateRangeSelect) {
      onDateRangeSelect(selectedStartDate, selectedEndDate);
    }
    onClose?.();
  }, [selectedStartDate, selectedEndDate, onDateRangeSelect, onClose]);

  const monthData = useMemo(
    () => getMonthData(currentMonth),
    [currentMonth, getMonthData],
  );

  const renderHeader = useMemo(
    () => (
      <View style={styles.header}>
        <Text style={styles.title}>Date Range</Text>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeText}>×</Text>
        </TouchableOpacity>
      </View>
    ),
    [onClose],
  );

  const renderMonthNavigation = useMemo(
    () => (
      <View style={styles.monthNavigation}>
        <TouchableOpacity onPress={() => navigateMonth('prev')}>
          <Text style={styles.navButton}>‹</Text>
        </TouchableOpacity>
        <Text style={styles.monthTitle}>
          {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
        </Text>
        <TouchableOpacity onPress={() => navigateMonth('next')}>
          <Text style={styles.navButton}>›</Text>
        </TouchableOpacity>
      </View>
    ),
    [currentMonth, navigateMonth],
  );

  const renderDayHeaders = useMemo(
    () => (
      <View style={styles.dayHeadersContainer}>
        <View style={styles.weekNumberHeader} />
        {dayNames.map(day => (
          <View key={day} style={styles.dayHeader}>
            <Text style={styles.dayHeaderText}>{day}</Text>
          </View>
        ))}
      </View>
    ),
    [],
  );

  const renderWeek = useCallback(
    (week: any[], weekIndex: number) => (
      <View key={weekIndex} style={styles.weekContainer}>
        <View style={styles.weekNumber}>
          <Text style={styles.weekNumberText}>{week[0]?.weekNumber || ''}</Text>
        </View>
        {week.map((day, dayIndex) => {
          const isInRange = isDateInRange(day.date);
          const isSelected = isDateSelected(day.date);
          const isToday = new Date().toDateString() === day.date.toDateString();
          const isSaturday = day.date.getDay() === 6;
          const isSunday = day.date.getDay() === 0;

          return (
            <TouchableOpacity
              key={dayIndex}
              style={[
                styles.dayContainer,
                isInRange && styles.dayInRange,
                isSelected && styles.daySelected,
              ]}
              onPress={() => handleDatePress(day.date)}>
              <Text
                style={[
                  styles.dayText,
                  !day.isCurrentMonth && styles.dayTextDisabled,
                  isToday && styles.dayTextToday,
                  isSelected && styles.dayTextSelected,
                  (isSaturday || isSunday) &&
                    day.isCurrentMonth &&
                    styles.dayTextWeekend,
                ]}>
                {day.date.getDate()}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    ),
    [isDateInRange, isDateSelected, handleDatePress],
  );

  return (
    <View style={styles.container}>
      {renderHeader}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderMonthNavigation}
        {renderDayHeaders}

        <View style={styles.calendarContainer}>
          {monthData.map((week, index) => renderWeek(week, index))}
        </View>

        <View style={styles.footer}>
          <TouchableOpacity
            style={[
              styles.applyButton,
              (!selectedStartDate || !selectedEndDate) &&
                styles.applyButtonDisabled,
            ]}
            onPress={handleApply}
            disabled={!selectedStartDate || !selectedEndDate}>
            <Text
              style={[
                styles.applyButtonText,
                (!selectedStartDate || !selectedEndDate) &&
                  styles.applyButtonTextDisabled,
              ]}>
              Apply
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: scale(20),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(20),
    marginBottom: scale(30),
  },
  title: {
    color: colors.white,
    fontSize: scale(20),
    fontFamily: fonts.SFPro.semiBold,
  },
  closeButton: {
    padding: scale(5),
  },
  closeText: {
    color: colors.white,
    fontSize: scale(24),
    fontFamily: fonts.SFPro.regular,
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(20),
  },
  monthNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: scale(20),
  },
  navButton: {
    color: colors.white,
    fontSize: scale(24),
    fontFamily: fonts.SFPro.medium,
    paddingHorizontal: scale(15),
    paddingVertical: scale(5),
  },
  monthTitle: {
    color: colors.white,
    fontSize: scale(18),
    fontFamily: fonts.SFPro.medium,
  },
  dayHeadersContainer: {
    flexDirection: 'row',
    marginBottom: scale(10),
  },
  weekNumberHeader: {
    width: scale(30),
  },
  dayHeader: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: scale(8),
  },
  dayHeaderText: {
    color: colors.white,
    fontSize: scale(14),
    fontFamily: fonts.SFPro.medium,
    opacity: 0.7,
  },
  calendarContainer: {
    marginBottom: scale(30),
  },
  weekContainer: {
    flexDirection: 'row',
    marginBottom: scale(2),
  },
  weekNumber: {
    width: scale(30),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.gray1,
    marginRight: scale(2),
    borderRadius: scale(4),
  },
  weekNumberText: {
    color: colors.white,
    fontSize: scale(12),
    fontFamily: fonts.SFPro.regular,
    opacity: 0.7,
  },
  dayContainer: {
    flex: 1,
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(4),
    marginHorizontal: scale(1),
  },
  dayInRange: {
    backgroundColor: colors.primary + '20',
  },
  daySelected: {
    backgroundColor: colors.primary,
  },
  dayText: {
    color: colors.white,
    fontSize: scale(16),
    fontFamily: fonts.SFPro.regular,
  },
  dayTextDisabled: {
    opacity: 0.3,
  },
  dayTextToday: {
    color: colors.yellow,
    fontFamily: fonts.SFPro.semiBold,
  },
  dayTextSelected: {
    color: colors.white,
    fontFamily: fonts.SFPro.semiBold,
  },
  dayTextWeekend: {
    color: colors.primary,
  },
  footer: {
    paddingBottom: scale(20),
  },
  applyButton: {
    backgroundColor: colors.primary,
    paddingVertical: scale(12),
    paddingHorizontal: scale(40),
    borderRadius: scale(8),
    alignItems: 'center',
  },
  applyButtonDisabled: {
    backgroundColor: colors.gray1,
  },
  applyButtonText: {
    color: colors.white,
    fontSize: scale(16),
    fontFamily: fonts.SFPro.semiBold,
  },
  applyButtonTextDisabled: {
    opacity: 0.5,
  },
});

export default DateRangeCalendar;
