import React, {memo} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';

interface IBRequestDetailProps {
  data: {
    id: number;
    company_name: string;
    company_phone: string;
    company_website: string;
    country: string;
    experience: string;
    services: string;
    status: number;
    campaign_name: string;
    created_at: string;
  };
  onClose?: () => void;
}

const IBRequestDetail: React.FC<IBRequestDetailProps> = ({data}) => {
  const getStatusColor = (status: number) => {
    switch (status) {
      case 3: // Approved
        return colors.green;
      case 1: // Requested
        return colors.white;
      case 5: // Rejected
        return colors.red;
      case 7: // Cancelled
        return colors.gray_text;
      default:
        return colors.yellow; // Pending
    }
  };

  const getStatusText = (status: number): string => {
    switch (status) {
      case 1:
        return 'Requested';
      case 3:
        return 'Approved';
      case 5:
        return 'Rejected';
      case 7:
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.idText}>ID: {data.id}</Text>

      <View style={styles.content}>
        <View style={styles.detailRow}>
          <Text style={styles.labelText}>Campaign Name</Text>
          <Text style={styles.valueText}>{data.campaign_name}</Text>
        </View>

        <View style={styles.separator} />

        <View style={styles.detailRow}>
          <Text style={styles.labelText}>Company Website</Text>
          <View style={styles.linkContainer}>
            <Text style={styles.valueText}>{data.company_website}</Text>
            <TouchableOpacity style={styles.copyButton}>
              <Image source={images.ic_copy} style={styles.copyIcon} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.separator} />

        <View style={styles.detailRow}>
          <Text style={styles.labelText}>Experience</Text>
          <Text style={styles.valueText}>{data.experience}</Text>
        </View>

        <View style={styles.separator} />

        <View style={styles.detailRow}>
          <Text style={styles.labelText}>Status</Text>
          <Text style={[styles.valueText, {color: getStatusColor(data.status)}]}>
            {getStatusText(data.status)}
          </Text>
        </View>

        <View style={styles.separator} />

        <View style={styles.detailRow}>
          <Text style={styles.labelText}>Date Created</Text>
          <Text style={styles.valueText}>{data.created_at}</Text>
        </View>

        <View style={styles.separator} />

        <View style={styles.detailRow}>
          <Text style={styles.labelText}>Company Name</Text>
          <Text style={styles.valueText}>{data.company_name}</Text>
        </View>

        <View style={styles.separator} />

        <View style={styles.detailRow}>
          <Text style={styles.labelText}>Company Phone</Text>
          <Text style={styles.valueText}>{data.company_phone}</Text>
        </View>

        <View style={styles.separator} />

        <View style={styles.detailRow}>
          <Text style={styles.labelText}>Country</Text>
          <Text style={styles.valueText}>{data.country}</Text>
        </View>

        <View style={styles.separator} />

        <View style={styles.detailRow}>
          <Text style={styles.labelText}>Services</Text>
          <Text style={styles.valueText}>{data.services}</Text>
        </View>

        <View style={styles.separator} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingHorizontal: scale(20),
    paddingTop: scale(20),
  },
  content: {
    marginTop: scale(30),
  },
  idText: {
    fontSize: scale(18),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    textAlign: 'left',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: scale(15),
  },
  separator: {
    height: 1,
    backgroundColor: colors.gray_border + '20',
    width: '100%',
  },
  labelText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
  valueText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    textAlign: 'right',
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  copyButton: {
    padding: scale(5),
    marginLeft: scale(5),
  },
  copyIcon: {
    width: scale(18),
    height: scale(18),
    tintColor: colors.white,
  },
});

export default memo(IBRequestDetail);
