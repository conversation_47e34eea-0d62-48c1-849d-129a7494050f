import React, {useMemo, memo, useCallback, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, ScrollView, Image, ActivityIndicator} from 'react-native';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button, Separator} from '../shared';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import IBRequestDetail from './IBRequestDetail';
import {useDispatch, useSelector} from 'react-redux';
import {referActions} from 'store/reducers/refer/actions';

interface RequestData {
  id: number;
  company_name: string;
  company_phone: string;
  company_website: string;
  country: string;
  experience: string;
  services: string;
  status: number;
  campaign_name: string;
  created_at: string;
}

interface RequestItemProps {
  data: RequestData;
  onCopyPress?: () => void;
  onCancelPress?: () => void;
  onPress?: () => void;
}

const getStatusText = (status: number): string => {
  switch (status) {
    case 1:
      return 'Requested';
    case 3:
      return 'Approved';
    case 5:
      return 'Rejected';
    case 7:
      return 'Cancelled';
    default:
      return 'Unknown';
  }
};

const RequestItem: React.FC<RequestItemProps> = memo(
  ({data, onCopyPress, onCancelPress, onPress}) => {
    const statusText = getStatusText(data.status);

    return (
      <TouchableOpacity
        activeOpacity={0.8}
        style={styles.requestRow}
        onPress={onPress}>
        <View style={styles.requestContent}>
          <Text style={styles.requestText}>{data.campaign_name}</Text>
          <View style={styles.linkContainer}>
            <Text style={styles.linkText}>{data.company_website}</Text>
            <TouchableOpacity
              onPress={(e) => {
                e.stopPropagation();
                onCopyPress && onCopyPress();
              }}
              style={styles.copyButton}>
              <Image source={images.ic_copy} style={styles.copyIcon} />
            </TouchableOpacity>
          </View>
          <Text style={[styles.requestText, styles.italicText]}>{data.experience}</Text>
          <Text
            style={[
              styles.requestText,
              data.status === 3 ? styles.approvedText :
              data.status === 1 ? styles.requestedText :
              data.status === 5 ? styles.rejectedText :
              data.status === 7 ? styles.cancelledText :
              {color: colors.yellow}, // Default to Pending color
            ]}
          >
            {statusText}
          </Text>
          <Text style={styles.requestText}>{data.created_at}</Text>
          {data.status === 1 && (
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={(e) => {
                e.stopPropagation();
                onCancelPress && onCancelPress();
              }}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          )}
          {data.status !== 1 && <View style={styles.emptyActionSpace} />}
        </View>
      </TouchableOpacity>
    );
  },
);

const IBRequest = () => {
  const dispatch = useDispatch();
  const {openBottomSheet} = useBottomSheet();
  const {data = [], loading = false, page = 1, last_page = 1} = useSelector(
    (state: any) => state.refer.getIBRequest || {},
  );

  useEffect(() => {
    dispatch(referActions.getIBRequest({page: 1, limit: 10}));
    return () => {
      dispatch(referActions.resetIBRequest());
    };
  }, [dispatch]);

  const handleLoadMore = useCallback(() => {
    if (page < last_page) {
      dispatch(referActions.getIBRequest({page: page + 1, limit: 10}));
    }
  }, [dispatch, page, last_page]);

  const handleRequestPress = useCallback((request: RequestData) => {
    setTimeout(() => {
      openBottomSheet({
        children: <IBRequestDetail data={request} />,
        showCloseBtn: true,
        enablePanDownToClose: true,
      });
    }, 0);
  }, [openBottomSheet]);

  const renderTableContent = useMemo(() => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        directionalLockEnabled={true}
        overScrollMode="never"
        bounces={false}
        style={styles.scrollContainer}>
        <View style={styles.tableContainer}>
          {/* Header */}
          <View style={styles.tableHeader}>
            <Text style={styles.headerText}>Campaign Name</Text>
            <Text style={styles.headerText}>Affiliate Link</Text>
            <Text style={styles.headerText}>Experience</Text>
            <Text style={styles.headerText}>Status</Text>
            <Text style={styles.headerText}>Date Created</Text>
            <View style={styles.headerActionColumn} />
          </View>

          <Separator />

          {/* Content */}
          <View style={styles.requestsWrapper}>
            {Array.isArray(data) && data.map((request: RequestData, index: number) => (
              <RequestItem
                key={`${request.id}-${index}`}
                data={request}
                onPress={() => handleRequestPress(request)}
                onCopyPress={() => console.log('Copy pressed for:', request.company_website)}
                onCancelPress={() => console.log('Cancel pressed for:', request)}
              />
            ))}
          </View>
        </View>
      </ScrollView>
    );
  }, [data, handleRequestPress]);

  const renderLoadMore = useMemo(
    () => {
      if (loading) {
        return (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={colors.yellow} />
          </View>
        );
      }

      if (page < last_page) {
        return (
          <Button
            title="Load More"
            variant="secondary"
            style={styles.loadMoreButton}
            onPress={handleLoadMore}
          />
        );
      }

      return null;
    },
    [loading, page, last_page, handleLoadMore],
  );

  return (
    <View style={styles.container}>
      <Separator />
      {Array.isArray(data) && data.length > 0 ? renderTableContent : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No IB requests found</Text>
        </View>
      )}
      {renderLoadMore}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  tableContainer: {
    flexDirection: 'column',
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: scale(10),
    backgroundColor: colors.black2,
    paddingHorizontal: scale(16),
  },
  headerText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    width: scale(150),
    marginRight: scale(20),
  },
  headerActionColumn: {
    width: scale(120),
  },
  requestsWrapper: {
    flexDirection: 'column',
  },
  requestRow: {
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  requestContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  requestText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    width: scale(150),
    marginRight: scale(20),
  },
  linkText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    marginRight: scale(5),
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    width: scale(150),
    marginRight: scale(20),
  },
  copyButton: {
    padding: scale(5),
  },
  copyIcon: {
    width: scale(18),
    height: scale(18),
    tintColor: colors.white,
  },
  italicText: {
    fontStyle: 'italic',
  },
  approvedText: {
    color: colors.green,
  },
  requestedText: {
    color: colors.white,
  },
  rejectedText: {
    color: colors.red,
  },
  cancelledText: {
    color: colors.gray_text,
  },
  defaultStatusText: {
    color: colors.yellow,
  },
  cancelButton: {
    backgroundColor: colors.yellow,
    paddingVertical: scale(8),
    paddingHorizontal: scale(12),
    borderRadius: scale(5),
    width: scale(120),
  },
  cancelButtonText: {
    fontSize: scale(14),
    fontFamily: fonts.SFPro.medium,
    color: colors.black,
    textAlign: 'center',
  },
  emptyActionSpace: {
    width: scale(120),
  },
  loadMoreButton: {
    marginVertical: scale(24),
    paddingHorizontal: scale(16),
    paddingVertical: scale(6),
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: scale(24),
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: scale(24),
  },
  emptyText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
});

export default memo(IBRequest);
