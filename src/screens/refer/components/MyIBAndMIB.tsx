import React, {memo, useCallback, useEffect, useMemo} from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {referActions} from 'store/reducers/refer/actions';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {Button, Separator} from '../shared';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import CommissionHistoryBottomSheet from './CommissionHistoryBottomSheet';
import DateRangeCalendar from './DateRangeCalendar';
import SearchBottomSheet from './SearchBottomSheet';

interface ClientData {
  id: number;
  login: number;
  campaign_name: string;
  group: {
    name: string;
  };
}

interface IBItemProps {
  accountType: string;
  accountID: string;
  campaignName: string;
  onPress?: () => void;
}

const IBItem: React.FC<IBItemProps> = memo(
  ({accountType, accountID, campaignName, onPress}) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.ibRow}
      onPress={onPress}>
      <Text style={styles.ibText}>{accountType}</Text>
      <Text style={styles.ibText}>{accountID}</Text>
      <Text style={[styles.ibText, styles.campaignText]}>{campaignName}</Text>
    </TouchableOpacity>
  ),
);

const MyIBAndMIB = () => {
  const dispatch = useDispatch();
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const {
    data: clients = [],
    loading = false,
    page = 1,
    last_page = 1,
  } = useSelector((state: any) => state.refer.getMyIBAndMIB || {});

  useEffect(() => {
    dispatch(referActions.getMyIBAndMIB({page: 1, limit: 3}));
    return () => {
      dispatch(referActions.resetMyIBAndMIB());
    };
  }, [dispatch]);

  const handleLoadMore = useCallback(() => {
    if (page < last_page) {
      dispatch(referActions.getMyIBAndMIB({page: page + 1, limit: 3}));
    }
  }, [dispatch, page, last_page]);

  const openCommissionHistoryBottomSheet = useCallback(
    (accountNumber: string) => {
      openBottomSheet({
        children: (
          <CommissionHistoryBottomSheet
            accountNumber={accountNumber}
            onClose={closeBottomSheet}
            onCalendarPress={() => {
              console.log('Calendar press from CommissionHistory');
              closeBottomSheet();

              setTimeout(() => {
                console.log('Opening DateRangeCalendar...');
                openBottomSheet({
                  children: (
                    <DateRangeCalendar
                      onClose={closeBottomSheet}
                      onDateRangeSelect={(startDate, endDate) => {
                        console.log('Date range selected:', startDate, endDate);
                        // TODO: Implement date filtering for commission history
                        // Format dates for API: MM/DD/YYYY-MM/DD/YYYY
                        closeBottomSheet();

                        setTimeout(() => {
                          openCommissionHistoryBottomSheet(accountNumber);
                        }, 300);
                      }}
                    />
                  ),
                  showCloseBtn: false,
                  enablePanDownToClose: true,
                });
              }, 800);
            }}
            onSearchPress={() => {
              console.log('🔍 Search icon clicked from CommissionHistory!');
              closeBottomSheet();

              setTimeout(() => {
                console.log('🔍 About to open SearchBottomSheet...');
                openBottomSheet({
                  children: (
                    <SearchBottomSheet
                      onClose={closeBottomSheet}
                      onSearch={searchTerm => {
                        console.log('🔍 Search term:', searchTerm);
                        // TODO: Implement search functionality for commission history
                        closeBottomSheet();

                        setTimeout(() => {
                          openCommissionHistoryBottomSheet(accountNumber);
                        }, 300);
                      }}
                    />
                  ),
                  showCloseBtn: false,
                  enablePanDownToClose: true,
                });
              }, 800);
            }}
          />
        ),
        showCloseBtn: false,
        enablePanDownToClose: true,
      });
    },
    [openBottomSheet, closeBottomSheet],
  );

  const handleIBPress = useCallback(
    (client: ClientData) => {
      const accountNumber = client?.login?.toString() || 'N/A';
      openCommissionHistoryBottomSheet(accountNumber);
    },
    [openCommissionHistoryBottomSheet],
  );

  const renderTableHeader = useMemo(
    () => (
      <>
        <Separator />
        <View style={styles.tableHeader}>
          <Text style={styles.headerText}>Account Type</Text>
          <Text style={styles.headerText}>Account ID</Text>
          <Text style={styles.headerText}>Campaign Name</Text>
        </View>
        <Separator />
      </>
    ),
    [],
  );

  const renderIBs = useMemo(
    () => (
      <View style={styles.ibsContainer}>
        {Array.isArray(clients) &&
          clients.map((client: ClientData, index: number) => {
            const accountType = client?.group?.name || 'N/A';
            const accountID = client?.login?.toString() || 'N/A';
            const campaignName = client?.campaign_name || 'N/A';

            return (
              <IBItem
                key={`${client?.id || index}-${
                  client?.login || index
                }-${index}`}
                accountType={accountType}
                accountID={accountID}
                campaignName={campaignName}
                onPress={() => handleIBPress(client)}
              />
            );
          })}
      </View>
    ),
    [clients, handleIBPress],
  );

  const renderLoadMore = useMemo(() => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.yellow} />
        </View>
      );
    }

    if (page < last_page) {
      return (
        <Button
          title="Load More"
          variant="secondary"
          style={styles.loadMoreButton}
          onPress={handleLoadMore}
        />
      );
    }

    return null;
  }, [loading, page, last_page, handleLoadMore]);

  return (
    <View style={styles.container}>
      {renderTableHeader}
      {Array.isArray(clients) && clients.length > 0 ? (
        renderIBs
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No IB & MIB found</Text>
        </View>
      )}
      {renderLoadMore}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: scale(10),
    backgroundColor: colors.black2,
    paddingHorizontal: scale(16),
  },
  headerText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    width: '33%',
  },
  ibsContainer: {
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  ibRow: {
    flexDirection: 'row',
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  ibText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    width: '33%',
  },
  campaignText: {
    fontStyle: 'italic',
  },
  loadMoreButton: {
    marginVertical: scale(24),
    paddingHorizontal: scale(16),
    paddingVertical: scale(6),
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: scale(24),
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: scale(24),
  },
  emptyText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
});

export default memo(MyIBAndMIB);
