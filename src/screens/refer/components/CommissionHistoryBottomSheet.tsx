import React, {useMemo, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button} from '../shared';
import {referActions} from 'store/reducers/refer/actions';

interface CommissionData {
  deal: number;
  login: number;
  time: string;
  symbol: string;
  action: string;
  price: number;
  profit: number;
  comment: string;
  volume: number;
  positionid: number;
  dealagent?: {
    deal: number;
    login: number;
    time: string;
    symbol: string;
    action: string;
    price: number;
    profit: number;
    comment: string;
    volume: number;
    positionid: number;
  };
}

interface CommissionHistoryBottomSheetProps {
  accountNumber: string;
  onClose?: () => void;
  onCalendarPress?: () => void;
}

const CommissionHistoryBottomSheet: React.FC<
  CommissionHistoryBottomSheetProps
> = ({accountNumber, onClose, onCalendarPress}) => {
  const dispatch = useDispatch();
  const {
    data = [],
    loading = false,
    page = 1,
    last_page = 1,
  } = useSelector((state: any) => state.refer.getCommissionHistory || {});

  const {data: totalCommission = 0, loading: totalCommissionLoading = false} =
    useSelector((state: any) => state.refer.getTotalCommission || {});

  useEffect(() => {
    dispatch(
      referActions.getCommissionHistory({
        accountId: accountNumber,
        orderBy: 'Time',
        sortedBy: 'DESC',
        page: 1,
        limit: 10,
        include: 'dealagent',
      }),
    );

    // Get total commission
    dispatch(
      referActions.getTotalCommission({
        accountId: accountNumber,
        orderBy: 'Time',
        sortedBy: 'DESC',
        page: 1,
        limit: 10,
      }),
    );

    return () => {
      dispatch(referActions.resetCommissionHistory());
      dispatch(referActions.resetTotalCommission());
    };
  }, [dispatch, accountNumber]);

  const handleLoadMore = useCallback(() => {
    if (page < last_page) {
      dispatch(
        referActions.getCommissionHistory({
          accountId: accountNumber,
          orderBy: 'Time',
          sortedBy: 'DESC',
          page: page + 1,
          limit: 10,
          include: 'dealagent',
        }),
      );
    }
  }, [dispatch, accountNumber, page, last_page]);

  const handleCalendarPress = useCallback(() => {
    if (onCalendarPress) {
      onCalendarPress();
    }
  }, [onCalendarPress]);

  const formatDateTime = useCallback((dateTimeString: string) => {
    const date = new Date(dateTimeString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day}  ${hours}:${minutes}:${seconds}`;
  }, []);

  const renderTableHeader = useMemo(
    () => (
      <View style={styles.tableHeader}>
        <Text style={styles.headerText}>Time</Text>
        <Text style={styles.headerText}>Trading Account</Text>
        <Text style={styles.headerText}>Position</Text>
      </View>
    ),
    [],
  );

  const renderTableRow = useCallback(
    (item: CommissionData) => (
      <View key={item.deal} style={styles.tableRow}>
        <Text style={styles.cellText}>{formatDateTime(item.time)}</Text>
        <Text style={styles.cellText}>{item.dealagent?.login || 'N/A'}</Text>
        <Text style={[styles.cellText, styles.symbolText]}>
          {item.dealagent?.positionid || 'N/A'}
        </Text>
      </View>
    ),
    [formatDateTime],
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.titleText}>Commission history of </Text>
          <Text style={styles.accountText}>{accountNumber}</Text>
        </View>
        <View style={styles.iconContainer}>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={handleCalendarPress}>
            <Image source={images.ic_calendar} style={styles.icon} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Image source={images.ic_search} style={styles.icon} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={onClose}>
            <Image source={images.ic_close} style={styles.closeIcon} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.tableWrapper}>
        <View style={styles.tableContainer}>
          {renderTableHeader}
          <ScrollView showsVerticalScrollIndicator={false}>
            {data.length > 0 ? (
              <>
                {data.map(renderTableRow)}
                {page < last_page && (
                  <View style={styles.loadMoreContainer}>
                    <Button
                      title={loading ? 'Loading...' : 'Load More'}
                      variant="secondary"
                      style={styles.loadMoreButton}
                      onPress={handleLoadMore}
                      disabled={loading}
                    />
                  </View>
                )}
              </>
            ) : (
              !loading && (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    No commission history found
                  </Text>
                </View>
              )
            )}
          </ScrollView>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Total commission:{' '}
          {totalCommissionLoading ? 'Loading...' : `${totalCommission} USD`}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: scale(20),
    paddingBottom: scale(80),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(20),
    marginBottom: scale(30),
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    color: colors.white,
    fontSize: scale(20),
    fontFamily: fonts.IBMPlexSans.medium,
  },
  accountText: {
    color: colors.yellow,
    fontSize: scale(20),
    fontFamily: fonts.IBMPlexSans.medium,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: scale(20),
  },
  icon: {
    width: scale(20),
    height: scale(20),
    tintColor: colors.white,
  },
  closeIcon: {
    width: scale(16),
    height: scale(16),
    tintColor: colors.white,
  },
  tableWrapper: {
    flex: 1,
    marginBottom: scale(20),
  },
  tableContainer: {
    flex: 1,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: colors.black2,
    paddingVertical: scale(12),
    paddingHorizontal: scale(20),
    borderBottomWidth: 1,
    borderBottomColor: colors.white + '10',
  },
  headerText: {
    color: colors.white,
    fontSize: scale(13),
    fontFamily: fonts.SFPro.medium,
    flex: 1,
    opacity: 0.9,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: scale(15),
    paddingHorizontal: scale(20),
    borderBottomWidth: 1,
    borderBottomColor: colors.white + '10',
  },
  cellText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    flex: 1,
    opacity: 0.9,
  },
  symbolText: {
    fontFamily: fonts.SFPro.semiBold,
    fontStyle: 'italic',
  },
  loadMoreContainer: {
    alignItems: 'center',
    paddingVertical: scale(20),
    paddingHorizontal: scale(20),
  },
  loadMoreButton: {
    paddingHorizontal: scale(40),
    paddingVertical: scale(8),
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.gray_border + '20',
    borderRadius: scale(5),
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: scale(40),
    paddingHorizontal: scale(20),
  },
  emptyText: {
    color: colors.white,
    fontSize: scale(16),
    fontFamily: fonts.SFPro.regular,
    opacity: 0.7,
    textAlign: 'center',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: scale(20),
    paddingBottom: scale(20),
    backgroundColor: colors.black,
  },
  footerText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    opacity: 0.9,
    marginBottom: scale(5),
  },
});

export default CommissionHistoryBottomSheet;
