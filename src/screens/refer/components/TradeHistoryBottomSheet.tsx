import React, {useState, useMemo, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button} from '../shared';

interface TradeHistoryData {
  id: string;
  time: string;
  position: string;
  symbol: string;
  typeOut: string;
  volume: string;
  price: string;
  closeTime: string;
  closePrice: string;
  profit: string;
}

interface TradeHistoryBottomSheetProps {
  accountNumber: string;
  onClose?: () => void;
}

// Mock data for trade history
const mockTradeHistory: TradeHistoryData[] = [
  {
    id: '1',
    time: '2020-11-10  00:45:20',
    position: '12548',
    symbol: 'CADCA.yz',
    typeOut: 'Sell',
    volume: '0.30 USD',
    price: '0.15125',
    closeTime: '2020-11-10  00:45:20',
    closePrice: '0.31541',
    profit: '-114.20 USD',
  },
  {
    id: '2',
    time: '2020-11-10  00:45:20',
    position: '34421',
    symbol: 'CAFADCA.yz',
    typeOut: 'Sell',
    volume: '0.30 USD',
    price: '0.15125',
    closeTime: '2020-11-10  00:45:20',
    closePrice: '0.31541',
    profit: '-114.20 USD',
  },
  {
    id: '3',
    time: '2020-11-10  00:45:20',
    position: '1267548',
    symbol: 'CAFADACA.yz',
    typeOut: 'Sell',
    volume: '0.30 USD',
    price: '0.15125',
    closeTime: '2020-11-10  00:45:20',
    closePrice: '0.31541',
    profit: '-114.20 USD',
  },
];

const TradeHistoryBottomSheet: React.FC<TradeHistoryBottomSheetProps> = ({
  accountNumber,
  onClose,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [data, setData] = useState<TradeHistoryData[]>(mockTradeHistory);
  const [hasMoreData, setHasMoreData] = useState(true);

  const handleLoadMore = useCallback(() => {
    if (currentPage < 3) {
      const newData = mockTradeHistory.map((item, index) => ({
        ...item,
        id: `${currentPage + 1}-${index + 1}`,
        position: (parseInt(item.position, 10) + currentPage * 1000).toString(),
      }));
      setData(prevData => [...prevData, ...newData]);
      setCurrentPage(prev => prev + 1);
    } else {
      setHasMoreData(false);
    }
  }, [currentPage]);

  const renderTableHeader = useMemo(
    () => (
      <View style={styles.tableHeader}>
        <Text style={styles.headerText}>Time</Text>
        <Text style={styles.headerText}>Position</Text>
        <Text style={styles.headerText}>Symbol</Text>
        <Text style={styles.headerText}>Type Out</Text>
        <Text style={styles.headerText}>Volume</Text>
        <Text style={styles.headerText}>Price</Text>
        <Text style={styles.headerText}>Time</Text>
        <Text style={styles.headerText}>Price</Text>
        <Text style={styles.headerText}>Profit</Text>
      </View>
    ),
    [],
  );

  const renderTableRow = useCallback(
    (item: TradeHistoryData) => (
      <View key={item.id} style={styles.tableRow}>
        <Text style={styles.cellText}>{item.time}</Text>
        <Text style={styles.cellText}>{item.position}</Text>
        <Text style={[styles.cellText, styles.symbolText]}>{item.symbol}</Text>
        <Text style={styles.cellText}>{item.typeOut}</Text>
        <Text style={styles.cellText}>{item.volume}</Text>
        <Text style={styles.cellText}>{item.price}</Text>
        <Text style={styles.cellText}>{item.closeTime}</Text>
        <Text style={styles.cellText}>{item.closePrice}</Text>
        <Text style={styles.cellText}>{item.profit}</Text>
      </View>
    ),
    [],
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.titleText}>Trade history of </Text>
          <Text style={styles.accountText}>{accountNumber}</Text>
        </View>
        <View style={styles.iconContainer}>
          <TouchableOpacity style={styles.iconButton}>
            <Image source={images.ic_calendar} style={styles.icon} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Image source={images.ic_search} style={styles.icon} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={onClose}>
            <Image source={images.ic_close} style={styles.closeIcon} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Table */}
      <View style={styles.tableWrapper}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.tableContainer}>
            {renderTableHeader}
            <ScrollView
              showsVerticalScrollIndicator={false}
              style={styles.tableContent}>
              {data.map(renderTableRow)}
            </ScrollView>
          </View>
        </ScrollView>

        {/* Load More Button */}
        {hasMoreData && (
          <View style={styles.loadMoreContainer}>
            <Button
              title="Load More"
              variant="secondary"
              style={styles.loadMoreButton}
              onPress={handleLoadMore}
            />
          </View>
        )}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>Profit: -44,716.70 USD</Text>
        <Text style={styles.footerText}>Volume: 13.56</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: scale(20),
    paddingBottom: scale(80),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(20),
    marginBottom: scale(30),
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    color: colors.white,
    fontSize: scale(20),
    fontFamily: fonts.IBMPlexSans.medium,
  },
  accountText: {
    color: colors.yellow,
    fontSize: scale(20),
    fontFamily: fonts.IBMPlexSans.medium,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: scale(20),
  },
  icon: {
    width: scale(20),
    height: scale(20),
    tintColor: colors.white,
  },
  closeIcon: {
    width: scale(16),
    height: scale(16),
    tintColor: colors.white,
  },
  tableWrapper: {
    flex: 1,
  },
  tableContainer: {
    minWidth: scale(1250),
  },
  tableContent: {
    maxHeight: scale(400),
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: colors.black2,
    paddingVertical: scale(12),
    paddingHorizontal: scale(20),
    borderBottomWidth: 1,
    borderBottomColor: colors.white + '10',
  },
  headerText: {
    color: colors.white,
    fontSize: scale(13),
    fontFamily: fonts.SFPro.medium,
    width: scale(140),
    opacity: 0.9,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: scale(15),
    paddingHorizontal: scale(20),
    borderBottomWidth: 1,
    borderBottomColor: colors.white + '10',
  },
  cellText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    width: scale(140),
    opacity: 0.9,
  },
  symbolText: {
    fontFamily: fonts.SFPro.semiBold,
    fontStyle: 'italic',
  },
  loadMoreContainer: {
    alignItems: 'center',
    paddingVertical: scale(15),
    paddingHorizontal: scale(20),
  },
  loadMoreButton: {
    paddingHorizontal: scale(40),
    paddingVertical: scale(8),
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.gray_border + '20',
    borderRadius: scale(5),
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: scale(20),
    paddingBottom: scale(20),
    backgroundColor: colors.black,
  },
  footerText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    opacity: 0.9,
    marginBottom: scale(5),
  },
});

export default TradeHistoryBottomSheet;
