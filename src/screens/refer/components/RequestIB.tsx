import React, {useState, useCallback, memo, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Image,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button} from '../shared';
import {useDispatch, useSelector} from 'react-redux';
import {referActions} from 'store/reducers/refer/actions';
import {useGlobalContext} from 'context/GlobalContext';

interface RequestIBProps {
  onBack?: () => void;
  onSuccess?: () => void;
}

const RequestIB: React.FC<RequestIBProps> = ({onBack, onSuccess}) => {
  const dispatch = useDispatch();
  const {showMessage} = useGlobalContext();
  const [campaignName, setCampaignName] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [companyPhone, setCompanyPhone] = useState('');
  const [companyWebsite, setCompanyWebsite] = useState('');
  const [experience, setExperience] = useState('');
  const [services, setServices] = useState('');
  const {error, success, message, loading} = useSelector(
    (state: any) => state.refer.createIBRequest || {}
  );

  useEffect(() => {
    return () => {
      dispatch(referActions.resetCreateIBRequest());
    };
  }, [dispatch]);

  useEffect(() => {
    if (success) {
      onSuccess && onSuccess();
    } else if (error) {
      showMessage({
        message: message || 'Failed to create IB request. Please try again.',
        type: 'error',
      });
    }
  }, [success, error, message, onSuccess, showMessage]);

  const handleRequest = useCallback(() => {
    if (!campaignName || !companyName || !companyPhone || !companyWebsite) {
      showMessage({
        message: 'Please fill in all required fields',
        type: 'error',
      });
      return;
    }

    const requestData = {
      campaign_name: campaignName,
      company_name: companyName,
      company_phone: companyPhone,
      company_website: companyWebsite,
      experience: experience,
      services: services,
      country_code: 'VN',
    };

    dispatch(referActions.createIBRequest(requestData));
  }, [campaignName, companyName, companyPhone, companyWebsite, experience, services, dispatch, showMessage]);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Image source={images.ic_back} style={styles.backIcon} />
          <Text style={styles.backText}>Back</Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.title}>Request New IB</Text>

      <View style={styles.formContainer}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Campaign Name</Text>
          <View style={styles.inputWithCurrency}>
            <TextInput
              style={styles.input}
              value={campaignName}
              onChangeText={setCampaignName}
              placeholder="Enter campaign name"
              placeholderTextColor={colors.gray_border}
            />
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Company Name</Text>
          <View style={styles.inputWithCurrency}>
            <TextInput
              style={styles.input}
              value={companyName}
              onChangeText={setCompanyName}
              placeholder="Enter company name"
              placeholderTextColor={colors.gray_border}
            />
            <View style={styles.currencyContainer}>
              <Text style={styles.currencyText}>USD</Text>
              <Image source={images.ic_dropdown} style={styles.dropdownIcon} />
            </View>
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Company Phone</Text>
          <View style={styles.inputWithCurrency}>
            <TextInput
              style={styles.input}
              value={companyPhone}
              onChangeText={setCompanyPhone}
              placeholder="Enter company phone"
              placeholderTextColor={colors.gray_border}
              keyboardType="phone-pad"
            />
            <View style={styles.currencyContainer}>
              <Text style={styles.currencyText}>USD</Text>
              <Image source={images.ic_dropdown} style={styles.dropdownIcon} />
            </View>
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Company Website</Text>
          <TouchableOpacity style={styles.dropdown}>
            <TextInput
              style={styles.input}
              value={companyWebsite}
              onChangeText={setCompanyWebsite}
              placeholder="Enter company website"
              placeholderTextColor={colors.gray_border}
            />
            <Image source={images.ic_dropdown} style={styles.dropdownIcon} />
          </TouchableOpacity>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Describe your experience...</Text>
          <View style={styles.textAreaContainer}>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={experience}
              onChangeText={setExperience}
              placeholder="Enter your experience"
              placeholderTextColor={colors.gray_border}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Describe your services...</Text>
          <View style={styles.textAreaContainer}>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={services}
              onChangeText={setServices}
              placeholder="Enter your services"
              placeholderTextColor={colors.gray_border}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>
        </View>
      </View>

      <View style={styles.buttonContainer}>
        {loading ? (
          <View style={[styles.requestButton, styles.loadingContainer]}>
            <ActivityIndicator size="small" color={colors.black} />
          </View>
        ) : (
          <Button
            title="Request"
            onPress={handleRequest}
            style={styles.requestButton}
            disabled={loading}
          />
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black2,
  },
  header: {
    paddingHorizontal: scale(20),
    paddingVertical: scale(15),
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backIcon: {
    width: scale(20),
    height: scale(20),
    tintColor: colors.white,
  },
  backText: {
    fontSize: scale(16),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginLeft: scale(8),
  },
  title: {
    fontSize: scale(24),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginTop: scale(10),
    marginBottom: scale(30),
    paddingHorizontal: scale(20),
  },
  formContainer: {
    paddingHorizontal: scale(20),
  },
  inputGroup: {
    marginBottom: scale(20),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    marginBottom: scale(8),
  },
  dropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray_border + '50',
    paddingBottom: scale(10),
  },
  input: {
    flex: 1,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    paddingVertical: scale(8),
  },
  dropdownIcon: {
    width: scale(16),
    height: scale(16),
    tintColor: colors.white,
  },
  inputWithCurrency: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray_border + '50',
    paddingBottom: scale(10),
  },
  currencyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencyText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    marginRight: scale(5),
  },
  textAreaContainer: {
    borderBottomWidth: 1,
    borderBottomColor: colors.gray_border + '50',
    width: '100%',
  },
  textArea: {
    height: scale(50),
    textAlignVertical: 'top',
    paddingBottom: scale(10),
    borderBottomWidth: 0,
  },
  buttonContainer: {
    alignItems: 'center',
    marginTop: scale(20),
    marginBottom: scale(40),
  },
  requestButton: {
    width: scale(150),
  },
  loadingContainer: {
    backgroundColor: colors.yellow,
    height: scale(32),
    borderRadius: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default memo(RequestIB);
