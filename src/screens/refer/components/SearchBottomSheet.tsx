import React, {useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button} from '../shared';
import {Image} from 'react-native';

interface SearchBottomSheetProps {
  onClose?: () => void;
  onSearch?: (searchTerm: string) => void;
}

const SearchBottomSheet: React.FC<SearchBottomSheetProps> = ({
  onClose,
  onSearch,
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = useCallback(() => {
    if (onSearch) {
      onSearch(searchTerm.trim());
    }
  }, [onSearch, searchTerm]);

  const handleClose = useCallback(() => {
    if (onClose) {
      onClose();
    }
  }, [onClose]);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Search</Text>
        <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
          <Image source={images.ic_close} style={styles.closeIcon} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Type</Text>
          <TextInput
            style={styles.input}
            value={searchTerm}
            onChangeText={setSearchTerm}
            placeholder="Enter search term..."
            placeholderTextColor={colors.white + '60'}
            autoFocus={true}
            returnKeyType="search"
            onSubmitEditing={handleSearch}
          />
        </View>

        <Button
          title="Search"
          variant="primary"
          style={styles.searchButton}
          onPress={handleSearch}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
    paddingTop: scale(20),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(20),
    marginBottom: scale(30),
  },
  title: {
    color: colors.white,
    fontSize: scale(20),
    fontFamily: fonts.IBMPlexSans.medium,
  },
  closeButton: {
    padding: scale(5),
  },
  closeIcon: {
    width: scale(16),
    height: scale(16),
    tintColor: colors.white,
  },
  content: {
    paddingHorizontal: scale(20),
  },
  inputContainer: {
    marginBottom: scale(30),
  },
  label: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    marginBottom: scale(10),
    opacity: 0.9,
  },
  input: {
    backgroundColor: colors.black2,
    borderWidth: 1,
    borderColor: colors.gray_border + '30',
    borderRadius: scale(8),
    paddingHorizontal: scale(15),
    paddingVertical: scale(12),
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
  searchButton: {
    backgroundColor: colors.yellow,
    paddingVertical: scale(12),
    borderRadius: scale(8),
  },
});

export default SearchBottomSheet;
