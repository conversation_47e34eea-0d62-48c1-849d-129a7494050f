import React, {memo, useMemo} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button} from '../shared';

interface InOutReportProps {
  ibId: string;
  onClose?: () => void;
}

interface TransactionData {
  account: string;
  deposit: string;
  withdraw: string;
}

const InOutReport: React.FC<InOutReportProps> = ({ibId, onClose}) => {
  const transactions: TransactionData[] = useMemo(
    () => [
      {
        account: '1510015',
        deposit: '0.50 USD',
        withdraw: '0.00 USD',
      },
      {
        account: '1512081',
        deposit: '10.20 USD',
        withdraw: '0.00 USD',
      },
      {
        account: '9221',
        deposit: '09.20 USD',
        withdraw: '0.00 USD',
      },
    ],
    [],
  );

  const renderHeader = useMemo(
    () => (
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>
            Trade history of <Text style={styles.ibId}>{ibId}</Text>
          </Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.iconButton}>
            <Image source={images.ic_calendar} style={styles.icon} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton} onPress={onClose}>
            <Image source={images.ic_close} style={styles.icon} />
          </TouchableOpacity>
        </View>
      </View>
    ),
    [ibId, onClose],
  );

  const renderTableHeader = useMemo(
    () => (
      <View style={styles.tableHeader}>
        <Text style={styles.headerText}>Account</Text>
        <Text style={styles.headerText}>Deposit</Text>
        <Text style={styles.headerText}>Withdraw</Text>
      </View>
    ),
    [],
  );

  const renderTransactions = useMemo(
    () => (
      <View style={styles.transactionsContainer}>
        {transactions.map((transaction, index) => (
          <View key={index} style={styles.transactionRow}>
            <Text style={styles.transactionText}>{transaction.account}</Text>
            <Text style={styles.transactionText}>{transaction.deposit}</Text>
            <Text style={[styles.transactionText, styles.italicText]}>
              {transaction.withdraw}
            </Text>
          </View>
        ))}
      </View>
    ),
    [transactions],
  );

  const renderLoadMore = useMemo(
    () => (
      <View style={styles.loadMoreContainer}>
        <Button
          title="Load More"
          variant="secondary"
          style={styles.loadMoreButton}
        />
      </View>
    ),
    [],
  );

  const renderFooter = useMemo(
    () => (
      <View style={styles.footer}>
        <Text style={styles.footerText}>Profit: -44,716.70 USD</Text>
        <Text style={styles.footerText}>Volume: 13.56</Text>
      </View>
    ),
    [],
  );

  return (
    <View style={styles.container}>
      {renderHeader}
      {renderTableHeader}
      {renderTransactions}
      {renderLoadMore}
      {renderFooter}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(20),
    paddingVertical: scale(15),
    backgroundColor: colors.black2,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: scale(18),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
  ibId: {
    color: colors.yellow,
    fontFamily: fonts.SFPro.medium,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    padding: scale(5),
    marginLeft: scale(15),
  },
  icon: {
    width: scale(20),
    height: scale(20),
    tintColor: colors.white,
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: scale(15),
    paddingHorizontal: scale(20),
    backgroundColor: colors.black,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray_border + '30',
  },
  headerText: {
    flex: 1,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  transactionsContainer: {
    borderBottomWidth: 1,
    borderBottomColor: colors.gray_border + '30',
  },
  transactionRow: {
    flexDirection: 'row',
    paddingVertical: scale(15),
    paddingHorizontal: scale(20),
    borderBottomWidth: 1,
    borderBottomColor: colors.gray_border + '20',
  },
  transactionText: {
    flex: 1,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
  italicText: {
    fontStyle: 'italic',
  },
  loadMoreContainer: {
    alignItems: 'center',
    paddingVertical: scale(20),
  },
  loadMoreButton: {
    paddingHorizontal: scale(20),
    paddingVertical: scale(8),
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: scale(15),
    paddingHorizontal: scale(20),
    backgroundColor: colors.black2,
  },
  footerText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginBottom: scale(5),
  },
});

export default memo(InOutReport);
