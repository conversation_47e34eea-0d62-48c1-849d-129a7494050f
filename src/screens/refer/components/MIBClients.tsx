import React, {useMemo, memo, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {But<PERSON>, Separator} from '../shared';
import {useDispatch, useSelector} from 'react-redux';
import {referActions} from 'store';
import {RootState} from 'store/types';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import TradeHistoryBottomSheet from './TradeHistoryBottomSheet';
import DateRangeCalendar from './DateRangeCalendar';
import SearchBottomSheet from './SearchBottomSheet';

interface ClientData {
  id: number;
  login: number;
  refer_code: string;
  campaign_name: string;
  created_at: string;
  user: {
    id: number;
    name: string;
    email: string;
    agent_account: number;
    agent_user_id: number;
  };
  group: {
    id: number;
    name: string;
    type: number;
    currency: string;
  };
  balance: {
    balance: number;
    credit: number;
    equity: number;
    marginfree: number;
    leverage: string;
  };
}

interface ClientItemProps {
  accountType: string;
  account: string;
  fullname: string;
  onPress?: () => void;
}

const ClientItem: React.FC<ClientItemProps> = memo(
  ({accountType, account, fullname, onPress}) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.clientRow}
      onPress={onPress}>
      <Text style={styles.clientText}>{accountType}</Text>
      <Text style={styles.clientText}>{account}</Text>
      <Text style={styles.clientText}>{fullname}</Text>
    </TouchableOpacity>
  ),
);

const MIBClients = () => {
  const dispatch = useDispatch();
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const {
    data: clients,
    loading,
    page,
    last_page,
  } = useSelector((state: RootState) => state.refer.getMIBClients);

  useEffect(() => {
    dispatch(referActions.referActions.getMIBClients({page: 1, limit: 3}));
  }, [dispatch]);

  const handleLoadMore = useCallback(() => {
    if (page < last_page) {
      dispatch(
        referActions.referActions.getMIBClients({page: page + 1, limit: 3}),
      );
    }
  }, [dispatch, page, last_page]);

  const openTradeHistoryBottomSheet = useCallback(
    (accountNumber: string) => {
      openBottomSheet({
        children: (
          <TradeHistoryBottomSheet
            accountNumber={accountNumber}
            onClose={closeBottomSheet}
            onCalendarPress={() => {
              closeBottomSheet();

              setTimeout(() => {
                openBottomSheet({
                  children: (
                    <DateRangeCalendar
                      onClose={closeBottomSheet}
                      onDateRangeSelect={(startDate, endDate) => {
                        console.log('Date range selected:', startDate, endDate);
                        closeBottomSheet();

                        setTimeout(() => {
                          openTradeHistoryBottomSheet(accountNumber);
                        }, 300);
                      }}
                    />
                  ),
                  showCloseBtn: false,
                  enablePanDownToClose: true,
                });
              }, 800);
            }}
            onSearchPress={() => {
              closeBottomSheet();

              setTimeout(() => {
                openBottomSheet({
                  children: (
                    <SearchBottomSheet
                      onClose={closeBottomSheet}
                      onSearch={searchTerm => {
                        console.log('Search term:', searchTerm);
                        closeBottomSheet();

                        setTimeout(() => {
                          openTradeHistoryBottomSheet(accountNumber);
                        }, 300);
                      }}
                    />
                  ),
                  showCloseBtn: false,
                  enablePanDownToClose: true,
                });
              }, 800);
            }}
          />
        ),
        showCloseBtn: false,
        enablePanDownToClose: true,
      });
    },
    [openBottomSheet, closeBottomSheet],
  );

  const handleClientPress = useCallback(
    (client: ClientData) => {
      const accountNumber = client?.login?.toString() || 'N/A';
      openTradeHistoryBottomSheet(accountNumber);
    },
    [openTradeHistoryBottomSheet],
  );

  const renderTableHeader = useMemo(
    () => (
      <>
        <Separator />
        <View style={styles.tableHeader}>
          <Text style={styles.headerText}>Account Type</Text>
          <Text style={styles.headerText}>Account</Text>
          <Text style={styles.headerText}>Fullname</Text>
        </View>
        <Separator />
      </>
    ),
    [],
  );

  const renderClients = useMemo(
    () => (
      <View style={styles.clientsContainer}>
        {clients.length > 0 ? (
          clients.map((client: ClientData, index: number) => {
            const accountType = client?.group?.name || 'N/A';
            const account = client?.login?.toString() || 'N/A';
            const fullname = client?.user?.name || 'N/A';

            return (
              <ClientItem
                key={`${client?.id || index}-${
                  client?.login || index
                }-${index}`}
                accountType={accountType}
                account={account}
                fullname={fullname}
                onPress={() => handleClientPress(client)}
              />
            );
          })
        ) : !loading ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No MIB clients found</Text>
          </View>
        ) : null}
      </View>
    ),
    [clients, loading, handleClientPress],
  );

  const renderLoadMore = useMemo(
    () =>
      loading ? (
        <ActivityIndicator
          color={colors.yellow}
          style={styles.loadMoreButton}
        />
      ) : page < last_page ? (
        <Button
          title="Load More"
          variant="secondary"
          style={styles.loadMoreButton}
          onPress={handleLoadMore}
        />
      ) : null,
    [loading, page, last_page, handleLoadMore],
  );

  return (
    <View style={styles.container}>
      {renderTableHeader}
      {renderClients}
      {renderLoadMore}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: scale(10),
    backgroundColor: colors.black2,
    paddingHorizontal: scale(16),
  },
  headerText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    width: '33%',
  },
  clientsContainer: {
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  clientRow: {
    flexDirection: 'row',
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  clientText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    width: '33%',
  },
  loadMoreButton: {
    marginVertical: scale(24),
    paddingHorizontal: scale(16),
    paddingVertical: scale(6),
  },
  emptyContainer: {
    paddingVertical: scale(24),
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
});

export default memo(MIBClients);
