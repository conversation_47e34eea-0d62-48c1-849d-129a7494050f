import React, {useMemo, memo, useCallback, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator} from 'react-native';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {But<PERSON>, Separator} from '../shared';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import InOutReport from './InOutReport';
import {useDispatch, useSelector} from 'react-redux';
import {referActions} from 'store/reducers/refer/actions';

interface IBItemProps {
  accountType: string;
  account: string;
  fullname: string;
  commission: string;
  dateCreated: string;
  onPress?: () => void;
  onReportPress?: () => void;
}

const IBItem: React.FC<IBItemProps> = memo(
  ({accountType, account, fullname, commission, dateCreated, onPress, onReportPress}) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.ibRow}
      onPress={onPress}>
      <View style={styles.ibContent}>
        <Text style={styles.ibText}>{accountType}</Text>
        <Text style={styles.ibText}>{account}</Text>
        <Text style={[styles.ibText, styles.italicText]}>{fullname}</Text>
        <Text style={styles.ibText}>{commission}</Text>
        <Text style={styles.ibText}>{dateCreated}</Text>
        <TouchableOpacity
          style={styles.reportButton}
          onPress={onReportPress}>
          <Text style={styles.reportButtonText}>In/Out Report</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  ),
);

const IBsUnder = () => {
  const dispatch = useDispatch();
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const {data = [], loading = false, page = 1, last_page = 1} = useSelector(
    (state: any) => state.refer.getIBsUnder || {},
  );

  useEffect(() => {
    dispatch(referActions.getIBsUnder({page: 1, limit: 10}));
    return () => {
      dispatch(referActions.resetIBsUnder());
    };
  }, [dispatch]);

  const handleLoadMore = useCallback(() => {
    if (page < last_page) {
      dispatch(referActions.getIBsUnder({page: page + 1, limit: 10}));
    }
  }, [dispatch, page, last_page]);

  const handleReportPress = useCallback((accountId: string) => {
    openBottomSheet({
      children: (
        <InOutReport
          ibId={accountId}
          onClose={closeBottomSheet}
        />
      ),
      showCloseBtn: false,
      enablePanDownToClose: true,
    });
  }, [openBottomSheet, closeBottomSheet]);

  const renderTableContent = useMemo(() => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        directionalLockEnabled={true}
        overScrollMode="never"
        bounces={false}
        style={styles.scrollContainer}>
        <View style={styles.tableContainer}>
          {/* Header */}
          <View style={styles.tableHeader}>
            <Text style={styles.headerText}>Account Type</Text>
            <Text style={styles.headerText}>Account</Text>
            <Text style={styles.headerText}>Fullname</Text>
            <Text style={styles.headerText}>Commission (USD)</Text>
            <Text style={styles.headerText}>Date Created</Text>
            <View style={styles.headerActionColumn} />
          </View>

          <Separator />

          {/* Content */}
          <View style={styles.ibsWrapper}>
            {Array.isArray(data) && data.map((item: any, index: number) => {
              const ibData = item.data;
              const accountType = ibData?.group?.name || 'N/A';
              const account = ibData?.account?.login?.toString() || 'N/A';
              const fullname = ibData?.user?.name || 'N/A';
              const commission = ibData?.commission?.toString() || '0';
              const dateCreated = ibData?.created_at || 'N/A';

              return (
                <IBItem
                  key={`${ibData?.id || index}`}
                  accountType={accountType}
                  account={account}
                  fullname={fullname}
                  commission={`${commission} USD`}
                  dateCreated={dateCreated}
                  onPress={() => console.log('IB pressed:', ibData)}
                  onReportPress={() => handleReportPress(account)}
                />
              );
            })}
          </View>
        </View>
      </ScrollView>
    );
  }, [data, handleReportPress]);

  const renderLoadMore = useMemo(
    () => {
      if (loading) {
        return (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={colors.yellow} />
          </View>
        );
      }

      if (page < last_page) {
        return (
          <Button
            title="Load More"
            variant="secondary"
            style={styles.loadMoreButton}
            onPress={handleLoadMore}
          />
        );
      }

      return null;
    },
    [loading, page, last_page, handleLoadMore],
  );

  return (
    <View style={styles.container}>
      <Separator />
      {Array.isArray(data) && data.length > 0 ? renderTableContent : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No IBs found</Text>
        </View>
      )}
      {renderLoadMore}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  tableContainer: {
    flexDirection: 'column',
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: scale(10),
    backgroundColor: colors.black2,
    paddingHorizontal: scale(16),
  },
  headerText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    width: scale(150),
    marginRight: scale(20),
  },
  headerActionColumn: {
    width: scale(120),
  },
  ibsWrapper: {
    flexDirection: 'column',
  },
  ibRow: {
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  ibContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ibText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    width: scale(150),
    marginRight: scale(20),
  },
  italicText: {
    fontStyle: 'italic',
  },
  reportButton: {
    backgroundColor: colors.black2,
    paddingVertical: scale(8),
    paddingHorizontal: scale(12),
    borderRadius: scale(5),
    borderWidth: 1,
    borderColor: colors.gray_border + '50',
    width: scale(120),
  },
  reportButtonText: {
    fontSize: scale(14),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    textAlign: 'center',
  },
  loadMoreButton: {
    marginVertical: scale(24),
    paddingHorizontal: scale(16),
    paddingVertical: scale(6),
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: scale(24),
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: scale(24),
  },
  emptyText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
});

export default memo(IBsUnder);
