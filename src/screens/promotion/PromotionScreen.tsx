import { useIsFocused, useNavigation } from '@react-navigation/native';
import { colors, fonts, images } from 'assets';
import { <PERSON><PERSON>, WrapperContainer } from 'components';
import { useGlobalContext } from 'context/GlobalContext';
import React, { useEffect } from 'react';
import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { promotionActions, screenFocusActions } from 'store';
import { scale } from 'utils/device';
import { convertDateYYMMDD } from 'utils/functions';

function PromotionScreen() {
  const navigation = useNavigation<any>();
  const isFocused = useIsFocused();
  const dispatch = useDispatch();
  const { handleLoading, showMessage } = useGlobalContext();

  const { loading, data, page, last_page } = useSelector(
    (state: any) => state.promotion.getPromotion,
  );

  const promotionList = [
    {
      title: 'NOVEMBER 2024 PROMOTION',
      image: images.tools_image,
      date: '2024/05/03 9:53 AM',
    },
  ];

  const onPressPromotionItem = item => {
    navigation.navigate('NewsDetailScreen', { data: item });
  };

  useEffect(() => {
    dispatch(promotionActions.resetPromotion());
  }, []);

  useEffect(() => {
    if (isFocused) {
      dispatch(screenFocusActions.setCurrentScreen('PromotionScreen'));
    }
  }, [isFocused]);

  useEffect(() => {
    dispatch(promotionActions.getPromotion({ page: page }));
  }, [page]);

  useEffect(() => {
    handleLoading(loading);
  }, [loading]);

  const onPressLoadMore = () => {
    dispatch(promotionActions.getPromotionUpdatePage(page + 1));
  };

  const renderPromotionItem = ({ item, index }) => (
    <TouchableOpacity
      onPress={() => onPressPromotionItem(item)}
      style={[
        styles.promotionItem,
        {
          borderBottomWidth: index === data?.length - 1 ? 1 : 0,
        },
      ]}>
      <Image source={{ uri: item?.image }} style={styles.promotionImage} />
      <View style={{ flex: 1, paddingHorizontal: scale(10) }}>
        <Text style={styles.title}>{item?.name}</Text>
        <Text style={styles.date}>
          {convertDateYYMMDD(item?.created_at, true, true)}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <WrapperContainer style={{ backgroundColor: colors.black2 }}>
      <Header />
      <View style={styles.titleRow}>
        <Text style={styles.screenTitle}>Promotions</Text>
        <TouchableOpacity style={styles.searchBtn}>
          <Image source={images.ic_search} style={styles.icSearch} />
        </TouchableOpacity>
      </View>
      <FlatList
        data={data}
        bounces={false}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderPromotionItem}
        style={{ marginTop: scale(44) }}
        ListFooterComponent={() =>
          page < last_page && (
            <TouchableOpacity
              style={styles.loadMoreBtn}
              onPress={onPressLoadMore}>
              <Text style={styles.loadMoreTxt}>Load More</Text>
            </TouchableOpacity>
          )
        }
      />
    </WrapperContainer>
  );
}

export default PromotionScreen;

const styles = StyleSheet.create({
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: scale(35),
    justifyContent: 'space-between',
    paddingHorizontal: scale(20),
  },
  screenTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(30),
    color: colors.white,
  },
  searchBtn: {
    width: scale(40),
    height: scale(40),
    backgroundColor: colors.black2,
    borderWidth: 1,
    borderColor: colors.gray_button,
    alignItems: 'center',
    justifyContent: 'center',
  },
  icSearch: {
    width: scale(24),
    height: scale(24),
  },
  promotionItem: {
    flexDirection: 'row',
    paddingVertical: scale(16),
    paddingHorizontal: scale(20),
    borderTopWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  promotionImage: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(25),
  },
  title: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
  },
  date: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(13),
    color: colors.white,
    marginTop: scale(7),
  },
  loadMoreBtn: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.black,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(5),
    marginTop: scale(20),
    marginBottom: scale(50),
  },
  loadMoreTxt: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
  },
});
