import Clipboard from '@react-native-clipboard/clipboard';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { ROOT_HTTP_REFFER } from 'api/constants';
import { colors, fonts, images } from 'assets';
import { Header, WrapperContainer } from 'components';
import { useBottomSheet } from 'context/BottomSheetModalContext';
import { useGlobalContext } from 'context/GlobalContext';
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Image,
  ImageBackground,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Deposit from 'screens/transaction/components/Deposit';
import Transfer from 'screens/transaction/components/Transfer';
import Withdraw from 'screens/transaction/components/Withdraw';
import {
  accountActions,
  announcementActions,
  promotionActions,
  screenFocusActions,
  transactionActions,
  userActions,
} from 'store';
import { Announcement } from 'store/types';
import { scale } from 'utils/device';
import {
  convertDateCreated,
  convertDateYYMMDD,
  formatTransactionType,
  renderTransactionIcon,
} from 'utils/functions';

import 'notification/NotificationService';
import DeviceInfo, { getApiLevel } from 'react-native-device-info';
import { getApp } from '@react-native-firebase/app';
import {
  getMessaging,
  requestPermission,
  AuthorizationStatus,
  getToken,
} from '@react-native-firebase/messaging';
import { requestNotifications } from 'react-native-permissions';
const messaging = getMessaging(getApp());
type NavigationProps = DrawerNavigationProp<any>;

interface AccountType {
  title: string;
  value: string;
}

export default function HomeScreen() {
  const navigation = useNavigation<NavigationProps>();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const { openBottomSheet, closeBottomSheet } = useBottomSheet();

  const { showToast, handleLoading } = useGlobalContext();

  const { totalBalance, login } = useSelector((state: any) => state.user);

  const promotion = useSelector((state: any) => state.promotion.getPromotion);

  const transaction = useSelector(
    (state: any) => state.transaction.getTransaction,
  );

  const deposit = useSelector((state: any) => state.transaction.getDeposit);
  const withdraw = useSelector((state: any) => state.transaction.getWithdraw);
  const transfer = useSelector((state: any) => state.transaction.getTransfer);

  const { loading, data, page, last_page } = useSelector(
    (state: any) => state.announcement.getAnnouncement,
  );

  const getAccount = useSelector(
    (state: any) => state.account.getAccount,
  );


  const [selectedTransactionType, setSelectedTransactionType] = useState({
    title: 'Recent History',
    value: 0,
  });

  const [transactionTypeList, setTransactionTypeList] = useState([
    {
      title: 'Recent History',
      value: 0,
    },
    {
      title: 'Deposit',
      value: 1,
    },
    {
      title: 'Withdraw',
      value: 2,
    },
    {
      title: 'Transfer',
      value: 3,
    },
  ]);


  const [typeList, setTypeList] = useState<AccountType[]>([
    {
      title: 'All',
      value: 'all',
    },
    {
      title: 'Wallet Account',
      value: 'wallet',
    },
    {
      title: 'Live Account',
      value: 'live',
    },
    {
      title: 'Demo Account',
      value: 'demo',
    },
  ]);

  const [selectedAccountType, setSelectedAccountType] = useState<AccountType>({
    title: 'All',
    value: 'all',
  });

  const transactionListData = (type: any) => {
    switch (type?.value) {
      case 0:
        return transaction?.data.slice(0, 6);
      case 1:
        return deposit?.data.slice(0, 6);
      case 2:
        return withdraw?.data.slice(0, 6);
      case 3:
        return transfer?.data.slice(0, 6);
      default:
        return transaction?.data.slice(0, 6);
    }
  };

  const getAccountRequest = (type: AccountType) => {
    let params = {
      include: 'user,group,balance',
      orderBy: 'created_at',
      sortedBy: 'DESC',
      page: 1,
      limit: 10
    }
    switch (type?.value) {
      case 'all':
        break;
      case 'wallet':
        params.isWallet = true
        break;
      case 'live':
        params.isLive = true
        break;
      case 'demo':
        params.isDemo = true
    }
    dispatch(accountActions.getAccount(params))
  }
  useEffect(() => {
    if (isFocused) {
      dispatch(screenFocusActions.setCurrentScreen('HomeScreen'));
    }
  }, [isFocused]);

  useEffect(() => {
    dispatch(userActions.getTotalBalance());
    requestUserPermission();
  }, []);

  const requestUserPermission = async () => {
    if (Platform.OS === 'android') {
      const apiLevel = await getApiLevel();

      if (apiLevel >= 33) {
        const { status } = await requestNotifications([
          'alert',
          'sound',
          'badge',
        ]);
        if (status !== 'granted') return;
      }
      registerAppWithFCM();
    }

    if (Platform.OS === 'ios') {
      const authStatus = await requestPermission(messaging);

      const enabled =
        authStatus === AuthorizationStatus.AUTHORIZED ||
        authStatus === AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        registerAppWithFCM();
      }
    }
  };

  const registerAppWithFCM = async () => {
    try {
      const tokenFCM = await getToken(messaging);
      console.log('tokenFCM', tokenFCM);
      // const device_id = await DeviceInfo.getUniqueId();
      // const device_name = await DeviceInfo.getDeviceName();
      // const device_type = Platform.OS === 'ios' ? 'ios' : 'android';
      // const appVersion = DeviceInfo.getVersion();
      // const deviceVersion = DeviceInfo.getSystemVersion();
      // const params: any = {
      //   device_id: device_id,
      //   device_name: device_name,
      //   device_os: device_type,
      //   device_token: tokenFCM,
      //   os_version: deviceVersion,
      //   language_code: 'vi', // vi or en
      //   app_version: appVersion,
      //   is_active: true,
      // };
      // dispatch(userActions.updateFCM(params));
    } catch (error) {
      console.log('ERROR REGISTER FCM', error);
    }
  };

  useEffect(() => {
    dispatch(announcementActions.getAnnouncement({ page: 1 }));
    dispatch(promotionActions.getPromotion({ page: 1, per_page: 10 }));
  }, []);

  useEffect(() => {
    dispatch(transactionActions.getTransaction({ page: 1, type: 0 }));
    dispatch(transactionActions.getTransaction({ page: 1, type: 1 }));
    dispatch(transactionActions.getTransaction({ page: 1, type: 2 }));
    dispatch(transactionActions.getTransaction({ page: 1, type: 3 }));
  }, []);

  useEffect(() => {
    getAccountRequest(selectedAccountType);
  }, [selectedAccountType?.value])



  useEffect(() => {
    if (
      promotion?.loading ||
      transaction?.loading ||
      deposit?.loading ||
      withdraw?.loading ||
      transfer?.loading ||
      loading || getAccount?.loading
    ) {
      handleLoading(true);
    } else {
      handleLoading(false);
    }
  }, [
    promotion?.loading,
    transaction?.loading,
    deposit?.loading,
    withdraw?.loading,
    transfer?.loading,
    loading,
    getAccount?.loading
  ]);



  const onPressOverview = useCallback(() => {
    navigation.navigate('AccountScreen');
  }, [navigation]);

  const renderBalance = useCallback(
    () => (
      <ImageBackground
        source={images.account_balance_background}
        style={styles.accountBalance}>
        <Image source={images.logo_tiger} style={styles.logo} />
        <Text style={styles.accountBalanceText}>Account Balance</Text>
        {totalBalance.loading ? (
          <ActivityIndicator
            color={colors.white}
            size={'large'}
            style={[styles.balance, { top: scale(115), left: scale(100) }]}
          />
        ) : (
          <Text style={styles.balance}>
            {totalBalance?.data?.total_balance} USD
          </Text>
        )}
        <TouchableOpacity onPress={onPressOverview} style={styles.overviewBtn}>
          <Text style={styles.overview}>Overview</Text>
        </TouchableOpacity>
      </ImageBackground>
    ),
    [totalBalance, onPressOverview],
  );

  const onPressDeposit = () => {
    openBottomSheet({
      children: (
        <Deposit
          openBottomSheet={openBottomSheet}
          closeBottomSheet={closeBottomSheet}
        />
      ),
      showCloseBtn: true,
    });
  };
  const onPressTransfer = () => {
    openBottomSheet({
      children: (
        <Transfer
          openBottomSheet={openBottomSheet}
          closeBottomSheet={closeBottomSheet}
        />
      ),
      showCloseBtn: true,
      showBackBtn: true,
    });
  };

  const onPressWithdraw = () => {
    openBottomSheet({
      children: (
        <Withdraw
          openBottomSheet={openBottomSheet}
          closeBottomSheet={closeBottomSheet}
        />
      ),
      showCloseBtn: true,
    });
  };
  const renderButton = useCallback(
    () => (
      <View style={{}}>
        <View
          style={{
            flexDirection: 'row',
            marginHorizontal: scale(20),
            justifyContent: 'space-between',
          }}>
          <TouchableOpacity onPress={onPressDeposit} style={styles.btn}>
            <Text style={styles.btnTitle}>Deposit</Text>
            <Image
              source={images.ic_deposit}
              style={[styles.icon, { marginLeft: scale(12) }]}
            />
          </TouchableOpacity>
          <TouchableOpacity onPress={onPressWithdraw} style={styles.btn}>
            <Image
              source={images.ic_deposit}
              style={[styles.icon, { marginRight: scale(12) }]}
            />
            <Text style={styles.btnTitle}>Withdraw</Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity onPress={onPressTransfer} style={styles.transferBtn}>
          <Image source={images.ic_transfer} style={styles.icon} />
          <Text style={styles.transferTitle}>Transfer</Text>
        </TouchableOpacity>
      </View>
    ),
    [],
  );

  const renderPromotion = useCallback(
    () => (
      <View style={styles.adsWrapper}>
        <FlatList
          horizontal
          bounces={false}
          data={promotion?.data}
          style={{ paddingHorizontal: scale(20) }}
          showsHorizontalScrollIndicator={false}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('NewsDetailScreen', { data: item })
              }
              style={{ width: scale(160) }}>
              <Image source={{ uri: item.image }} style={styles.adsImg} />
              <Text style={styles.adsTitle}>{item?.name}</Text>
            </TouchableOpacity>
          )}
          ItemSeparatorComponent={() => <View style={{ width: scale(15) }} />}
          ListFooterComponent={() => (
            <View
              style={{
                width: scale(20),
              }}
            />
          )}
        />
      </View>
    ),
    [promotion?.data, navigation],
  );

  const onPressCopy = () => {
    if (login.ib_refer_code) {
      Clipboard.setString(`${ROOT_HTTP_REFFER}/${login.ib_refer_code}`);
      showToast({
        type: 'info',
        message: 'Copy reffer link successfully',
        position: 'top',
      });
      return;
    }
  };

  const onPressQRCode = () => { };

  const onPressMore = () => { };

  const renderRef = useCallback(
    () => (
      <View style={styles.referView}>
        <Text style={styles.referLink}>Refer Link</Text>
        <TouchableOpacity onPress={onPressCopy} style={styles.copyBtn}>
          <Image source={images.ic_copy} style={styles.icCopy} />
          <Text style={styles.txtCopy}>Copy</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={onPressQRCode} style={styles.qrCodeBtn}>
          <Image source={images.ic_qrcode} style={styles.icQRCode} />
          <Text style={styles.qrCodeTxt}>QRcode</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onPressMore}
          style={{ marginLeft: scale(6), padding: scale(10) }}>
          <Image source={images.ic_more} style={styles.icMore} />
        </TouchableOpacity>
      </View>
    ),
    [],
  );


  const renderTransactionItem = ({ item }) => (
    <View style={styles.transactionRow}>
      <Text style={[styles.transactionInfo, { width: scale(140) }]}>
        {formatTransactionType(item?.type)}
      </Text>
      <View style={{ flexDirection: 'row', width: scale(150) }}>
        <Image
          source={renderTransactionIcon(item)}
          style={styles.icTransactionType}
        />
        <Text style={styles.transactionInfo}>${item?.amount}</Text>
      </View>
      <Text style={styles.transactionInfo}>
        {convertDateYYMMDD(item?.created_at)}
      </Text>
    </View>
  );



  const renderTransactionList = useCallback(() => {
    return (
      <View style={styles.transactionWrapper}>
        <View style={{ flexDirection: 'row' }}>
          {transactionTypeList.map(item => (
            <TouchableOpacity
              onPress={() => setSelectedTransactionType(item)}
              style={styles.transactionTypeBtn}>
              <Text
                style={[
                  styles.transactionTitle,
                  {
                    color:
                      item?.value === selectedTransactionType?.value
                        ? colors.yellow
                        : colors.white,
                  },
                ]}>
                {item?.title}
              </Text>
              {item?.value === selectedTransactionType?.value && (
                <View style={styles.dot} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        <FlatList
          showsVerticalScrollIndicator={false}
          data={transactionListData(selectedTransactionType)}
          renderItem={renderTransactionItem}
        />
        <TouchableOpacity
          onPress={() => navigation.navigate('TransactionScreen', {})}
          style={[
            styles.viewMoreBtn,
            { backgroundColor: colors.gray2, marginTop: scale(30) },
          ]}>
          <Text style={styles.viewMoreTxt}>View More</Text>
        </TouchableOpacity>
      </View>
    );
  }, [selectedTransactionType, transaction?.data]);

  const renderAccountItem = ({ item }: any) => (
    <TouchableOpacity style={styles.accountItem}>
      <Text style={styles.accountItemBalance}>{item?.balance?.balance} {item?.group?.currency}</Text>
      <Text style={styles.accountItemType}>{item?.group?.name}</Text>
      <Text style={styles.accountItemCode}>ID#{item?.id}</Text>
      <Text style={styles.accountItemCreated}>{item?.created_at}</Text>
      <Image
        source={images.ic_more}
        style={[styles.icMore, { marginTop: scale(22), marginBottom: scale(8) }]}
      />
    </TouchableOpacity>
  );



  const renderListAccount = useCallback(
    () => (
      <View style={{ marginTop: scale(30) }}>
        <View style={styles.accountListView}>
          {typeList.map((item: AccountType) => (
            <TouchableOpacity
              onPress={() => setSelectedAccountType(item)}
              style={{ paddingVertical: scale(6), marginRight: scale(10) }}>
              <Text
                style={{
                  fontFamily: fonts.SFPro.medium,
                  fontSize: scale(17),
                  color:
                    item?.value === selectedAccountType?.value
                      ? colors.yellow
                      : colors.white,
                }}>
                {item.title}
              </Text>
              {item?.value === selectedAccountType?.value && (
                <View style={styles.dot} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        <FlatList
          horizontal
          bounces={false}
          showsHorizontalScrollIndicator={false}
          style={{ paddingHorizontal: scale(20) }}
          data={getAccount?.data}
          renderItem={renderAccountItem}
          ItemSeparatorComponent={() => <View style={{ width: scale(15) }} />}
          ListFooterComponent={() => <View style={{ width: scale(20) }} />}
        />
        <TouchableOpacity
          onPress={() => navigation.navigate('AccountScreen')}
          style={[
            styles.viewMoreBtn,
            { backgroundColor: colors.gray2, marginTop: scale(30) },
          ]}>
          <Text style={styles.viewMoreTxt}>View More</Text>
        </TouchableOpacity>
      </View>
    ),
    [selectedAccountType, typeList, getAccount?.data],
  );

  const onPressViewMoreAnnouncement = () => {
    navigation.navigate('ToolsStack', {
      screen: 'AnnouncementScreen',
    });
  };

  const renderAnnouncement = useCallback(
    () => (
      <View style={styles.announcementView}>
        <Text style={styles.announcementTxt}>Announcement</Text>
        {data.map((item: Announcement) => (
          <TouchableOpacity
            onPress={() =>
              navigation.navigate('NewsDetailScreen', { data: item })
            }
            style={styles.announcementBtn}>
            <Text style={styles.announcementTitle}>{item?.name}</Text>
            <Text style={styles.announcementTime}>
              {convertDateCreated(item?.created_at)}
            </Text>
          </TouchableOpacity>
        ))}
        {page < last_page && (
          <TouchableOpacity
            onPress={onPressViewMoreAnnouncement}
            style={[
              styles.viewMoreBtn,
              { backgroundColor: colors.gray1, marginTop: scale(15) },
            ]}>
            <Text style={styles.viewMoreTxt}>View More</Text>
          </TouchableOpacity>
        )}
      </View>
    ),
    [data, page, last_page],
  );

  return (
    <WrapperContainer>
      <Header />
      <ScrollView
        style={styles.scrollView}
        bounces={false}
        showsVerticalScrollIndicator={false}>
        {renderBalance()}
        {renderButton()}
        {renderTransactionList()}
        {renderPromotion()}
        {renderRef()}
        {renderListAccount()}
        {renderAnnouncement()}
        {/* <View style={{height: scale(20)}} /> */}
      </ScrollView>
    </WrapperContainer>
  );
}

const styles = StyleSheet.create({
  scrollView: {},
  accountBalance: {
    height: scale(240),
    marginTop: scale(15),
    resizeMode: 'cover',
  },
  logo: {
    width: scale(50),
    height: scale(50),
    left: scale(35),
    top: scale(15),
  },
  accountBalanceText: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(13),
    textTransform: 'uppercase',
    color: colors.yellow,
    position: 'absolute',
    left: scale(35),
    top: scale(90),
  },
  balance: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(40),
    color: colors.white,
    position: 'absolute',
    left: scale(35),
    top: scale(105),
  },
  overviewBtn: {
    padding: scale(10),
    position: 'absolute',
    top: scale(22),
    right: scale(33),
  },
  overview: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(13),
    textTransform: 'uppercase',
    color: colors.white,
  },
  btn: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray2,
    paddingVertical: scale(13),
    width: scale(195),
    borderRadius: scale(10),
  },
  btnTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    color: colors.yellow,
  },
  icon: {
    width: scale(24),
    height: scale(24),
  },
  transferBtn: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: scale(400),
    alignSelf: 'center',
    alignItems: 'center',
    paddingVertical: scale(13),
    backgroundColor: colors.gray2,
    borderRadius: scale(10),
    marginTop: scale(15),
  },
  transferTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    marginLeft: scale(13),
    color: colors.white,
  },
  adsWrapper: {
    backgroundColor: colors.gray2,
    paddingVertical: scale(23),
    marginTop: scale(30),
  },
  adsImg: {
    width: scale(160),
    height: scale(105),
    borderRadius: scale(5),
  },
  adsTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
    marginTop: scale(10),
  },
  referView: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray2,
    marginTop: scale(24),
    marginHorizontal: scale(20),
    paddingHorizontal: scale(12),
    borderRadius: scale(10),
  },
  referLink: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    color: colors.white,
    padding: scale(10),
  },
  copyBtn: {
    flexDirection: 'row',
    backgroundColor: colors.yellow,
    width: scale(100),
    height: scale(30),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(5),
    marginHorizontal: scale(20),
  },
  icCopy: {
    width: scale(16),
    height: scale(16),
  },
  txtCopy: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.black,
    marginLeft: scale(7),
  },
  qrCodeBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: scale(10),
  },
  qrCodeTxt: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(17),
    color: colors.yellow,
    marginLeft: scale(6),
  },
  icQRCode: {
    width: scale(20),
    height: scale(20),
  },
  icMore: {
    width: scale(25),
    height: scale(5),
  },
  announcementView: {
    backgroundColor: colors.gray2,
    padding: scale(20),
    marginTop: scale(20),
  },
  announcementTxt: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    color: colors.white,
    marginBottom: scale(12),
  },
  announcementBtn: {
    marginVertical: scale(10),
  },
  announcementTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
  },
  announcementTime: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(13),
    color: colors.white,
    marginTop: scale(3),
  },
  viewMoreBtn: {
    width: scale(100),
    height: scale(30),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(5),
    alignSelf: 'center',
  },
  viewMoreTxt: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.yellow,
  },
  accountListView: {
    flexDirection: 'row',
    marginBottom: scale(20),
    paddingHorizontal: scale(20),
  },
  accountItem: {
    backgroundColor: colors.gray2,
    paddingVertical: scale(10),
    paddingHorizontal: scale(14),
    borderRadius: scale(10),
  },
  accountItemBalance: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(20),
    color: colors.white,
  },
  accountItemType: {
    fontFamily: fonts.SFPro.italic,
    fontSize: scale(13),
    color: colors.yellow,
    marginTop: scale(4),
  },
  accountItemCode: {
    fontFamily: fonts.SFPro.semiBold,
    fontWeight: '600',
    fontStyle: 'italic',
    fontSize: scale(13),
    color: colors.white,
    marginTop: scale(17),
  },
  accountItemCreated: {
    fontFamily: fonts.SFPro.semiBold,
    fontStyle: 'italic',
    fontWeight: '600',
    fontSize: scale(13),
    color: colors.white,
    marginTop: scale(7),
  },
  transactionTypeBtn: {
    paddingVertical: scale(10),
    marginRight: scale(30),
  },
  transactionInfo: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
  },
  transactionRow: {
    flexDirection: 'row',
    marginTop: scale(25),
  },
  icTransactionType: {
    width: scale(20),
    height: scale(20),
    marginRight: scale(7),
  },
  transactionWrapper: {
    marginTop: scale(24),
    paddingHorizontal: scale(20),
  },
  dot: {
    width: scale(5),
    height: scale(5),
    borderRadius: scale(5),
    backgroundColor: colors.yellow,
    alignSelf: 'center',
    marginTop: scale(3),
  },
  transactionTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
  },
});
