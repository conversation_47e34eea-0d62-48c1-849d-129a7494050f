import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import React, {useEffect, useState, useCallback, useMemo} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {useGlobalContext} from 'context/GlobalContext';
import {userActions} from 'store';
import {colors, fonts, images} from 'assets';
import {WrapperContainer, Dropdown} from 'components';
import {scale} from 'utils/device';
import {RootState} from 'store/types';
import {network} from 'api';
import {CommonActions} from '@react-navigation/native';

interface Props {
  navigation: any;
}

interface LoginInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
  secureTextEntry?: boolean;
  isDropdown?: boolean;
  dropdownData?: any[];
  onSelect?: (value: any) => void;
  valueKey?: string;
}

const LoginInput: React.FC<LoginInputProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
  secureTextEntry,
  isDropdown,
  dropdownData,
  onSelect,
  valueKey = 'code',
}) => {
  return (
    <View style={styles.inputWrapper}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.phoneInputContainer}>
        {isDropdown ? (
          <Dropdown
            data={dropdownData || []}
            selectedValue={value}
            onSelect={item => onSelect && onSelect(item[valueKey])}
            labelKey="code_name"
          />
        ) : (
          <TextInput
            style={styles.input}
            placeholder={placeholder}
            placeholderTextColor="#666"
            value={value}
            onChangeText={onChangeText}
            secureTextEntry={secureTextEntry}
          />
        )}
      </View>
    </View>
  );
};

interface LoginFormProps {
  selectedCountryCode: string;
  username: string;
  password: string;
  onCountryCodeChange: (code: string) => void;
  onUsernameChange: (text: string) => void;
  onPasswordChange: (text: string) => void;
  onLogin: () => void;
  onForgotPassword: () => void;
  countryList: any[];
}

const LoginForm: React.FC<LoginFormProps> = ({
  selectedCountryCode,
  username,
  password,
  onCountryCodeChange,
  onUsernameChange,
  onPasswordChange,
  onLogin,
  onForgotPassword,
  countryList,
}) => {
  return (
    <>
      <View style={styles.inputContainer}>
        <LoginInput
          label="Country Code"
          value={selectedCountryCode}
          onChangeText={() => {}}
          placeholder=""
          isDropdown
          dropdownData={countryList}
          onSelect={onCountryCodeChange}
        />
        <LoginInput
          label="Username"
          value={username}
          onChangeText={onUsernameChange}
          placeholder="Enter Username"
        />
        <LoginInput
          label="Password"
          value={password}
          onChangeText={onPasswordChange}
          placeholder="Enter Password"
          secureTextEntry
        />
      </View>
      <TouchableOpacity style={styles.loginButton} onPress={onLogin}>
        <Text style={styles.loginButtonText}>Log In</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={onForgotPassword}>
        <Text style={styles.forgotPassword}>Forgot Password?</Text>
      </TouchableOpacity>
    </>
  );
};
interface SocialLoginButtonsProps {
  onGoogleLogin?: () => void;
  onFacebookLogin?: () => void;
  onSignup: () => void;
}

const SocialLoginButtons: React.FC<SocialLoginButtonsProps> = ({
  onGoogleLogin,
  onFacebookLogin,
  onSignup,
}) => {
  return (
    <View style={styles.socialButtonsContainer}>
      {/* <TouchableOpacity style={styles.socialButton} onPress={onGoogleLogin}>
        <Image source={images.ic_logo_google} style={styles.socialButtonIcon} />
        <Text style={styles.socialButtonText}>Continue with Google</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.socialButton} onPress={onFacebookLogin}>
        <Image
          source={images.ic_logo_facebook}
          style={styles.socialButtonIcon}
        />
        <Text style={styles.socialButtonText}>Continue with Facebook</Text>
      </TouchableOpacity> */}

      <TouchableOpacity onPress={onSignup} style={styles.createAccountButton}>
        <Text style={styles.createAccountText}>
          Create a ESmileTradeFX Account
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const LoginScreen: React.FC<Props> = ({navigation}) => {
  const {showMessage} = useGlobalContext();
  const dispatch = useDispatch();
  const [selectedCountryCode, setSelectedCountryCode] = useState('');
  const [countryList, setCountryList] = useState<any[]>([]);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  const dataLogin = useSelector((state: RootState) => state.user.login);
  const dataCountry = useSelector((state: RootState) => state.user.country);

  useEffect(() => {
    dispatch(userActions.getCountry());
  }, []);

  useEffect(() => {
    if (dataCountry.data) {
      // setSelectedCountryCode(dataCountry.data[0]?.code);
      const dataCountryList = dataCountry.data?.map((item: any) => ({
        id: item.code,
        code: item.code,
        name: item.name,
        dial_code: item.dial_code,
        code_name: `${item.name} +${item.dial_code}`,
      }));
      setCountryList(dataCountryList);
    } else if (dataCountry.loading) {
      // Show loading state
      setCountryList([
        {
          id: 'loading',
          code: 'loading',
          name: 'Loading...',
          dial_code: '',
          code_name: 'Loading countries...',
        },
      ]);
    } else if (dataCountry.error) {
      // Show error state
      setCountryList([
        {
          id: 'error',
          code: 'error',
          name: 'Error loading countries',
          dial_code: '',
          code_name: 'Error loading countries',
        },
      ]);
      showMessage({
        message: dataCountry.message || 'Failed to load countries',
        type: 'error',
      });
    }
  }, [
    dataCountry.data,
    dataCountry.loading,
    dataCountry.error,
    dataCountry.message,
    showMessage,
  ]);

  const loginData = useMemo(
    () => ({
      country_code: selectedCountryCode,
      login: username,
      password: password,
    }),
    [selectedCountryCode, username, password],
  );

  const handleSignup = useCallback(() => {
    navigation.navigate('Signup');
  }, [navigation]);

  const handleLogin = useCallback(() => {
    if (!username || !password) {
      showMessage({
        message: 'Please fill all fields',
        type: 'error',
      });
      return;
    }

    if (dataCountry.loading) {
      showMessage({
        message: 'Please wait for countries to load',
        type: 'error',
      });
      return;
    }

    if (dataCountry.error) {
      showMessage({
        message: 'Please retry loading countries',
        type: 'error',
      });
      return;
    }

    dispatch(userActions.login(loginData));
  }, [
    username,
    password,
    loginData,
    dispatch,
    showMessage,
    dataCountry.loading,
    dataCountry.error,
  ]);

  const handleForgetPassword = useCallback(() => {
    navigation.navigate('ForgetPassword');
  }, [navigation]);

  const handleLoginSuccess = useCallback(async () => {
    if (dataLogin.success) {
      const token =
        dataLogin.data &&
        typeof dataLogin.data === 'object' &&
        'token' in dataLogin.data
          ? (dataLogin.data as {token: string}).token
          : '';
      await AsyncStorage.setItem('ACCESS_TOKEN', token);
      network.setToken(token);
      navigation.dispatch(
        CommonActions.reset({
          routes: [{name: 'MainStack'}],
        }),
      );
    } else if (dataLogin.error) {
      showMessage({
        message: dataLogin.message,
        type: 'error',
      });
    }
  }, [dataLogin, navigation, showMessage]);

  useEffect(() => {
    handleLoginSuccess();
  }, [dataLogin]);

  const handleUsernameChange = useCallback((text: string) => {
    setUsername(text);
  }, []);

  const handlePasswordChange = useCallback((text: string) => {
    setPassword(text);
  }, []);

  const handleCountryCodeChange = useCallback((code: string) => {
    setSelectedCountryCode(code);
  }, []);

  const handleRetryCountries = useCallback(() => {
    dispatch(userActions.getCountry());
  }, [dispatch]);

  return (
    <WrapperContainer style={styles.wrapper}>
      <View style={styles.container}>
        <Text style={styles.title}>Log In</Text>

        <LoginForm
          selectedCountryCode={selectedCountryCode}
          username={username}
          password={password}
          onCountryCodeChange={handleCountryCodeChange}
          onUsernameChange={handleUsernameChange}
          onPasswordChange={handlePasswordChange}
          onLogin={handleLogin}
          onForgotPassword={handleForgetPassword}
          countryList={countryList}
        />

        {dataCountry.error && (
          <TouchableOpacity
            style={styles.retryButton}
            onPress={handleRetryCountries}>
            <Text style={styles.retryButtonText}>Retry Loading Countries</Text>
          </TouchableOpacity>
        )}

        <SocialLoginButtons onSignup={handleSignup} />
      </View>
    </WrapperContainer>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: colors.black,
  },
  container: {
    flex: 1,
    paddingTop: scale(120),
    display: 'flex',
    flexDirection: 'column',
  },
  title: {
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
    marginBottom: scale(48),
    textAlign: 'center',
    color: colors.white,
  },
  inputContainer: {
    marginBottom: scale(26),
    gap: scale(16),
  },
  inputWrapper: {
    borderBottomWidth: 0.2,
    borderBottomColor: `${colors.white}80`,
    paddingHorizontal: scale(20),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  phoneInputContainer: {
    justifyContent: 'center',
    gap: scale(16),
    height: scale(56),
  },
  countryCode: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
  },
  countryCodeIcon: {
    width: scale(12),
    height: scale(12),
  },
  countryCodeText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
  input: {
    flex: 1,
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
  loginButton: {
    backgroundColor: colors.yellow,
    paddingVertical: scale(8),
    paddingHorizontal: scale(30),
    borderRadius: scale(4),
    alignItems: 'center',
    marginBottom: scale(20),
    alignSelf: 'center',
  },
  loginButtonText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  forgotPassword: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    textAlign: 'center',
    marginBottom: scale(20),
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(24),
    paddingVertical: scale(15),
    paddingHorizontal: scale(26),
    borderRadius: scale(8),
    alignSelf: 'center',
    borderWidth: 0.2,
    borderColor: `${colors.gray_border}80`,
    marginHorizontal: scale(20),
    marginBottom: scale(12),
  },
  socialButtonIcon: {
    width: scale(30),
    height: scale(30),
  },
  socialButtonText: {
    flex: 1,
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  socialButtonsContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingBottom: scale(24),
  },
  createAccountButton: {
    marginBottom: scale(20),
    alignItems: 'center',
    borderWidth: 0.2,
    borderColor: `${colors.gray_border}80`,
    paddingVertical: scale(8),
    paddingHorizontal: scale(12),
    borderRadius: scale(4),
    alignSelf: 'center',
    backgroundColor: colors.gray_button,
  },
  createAccountText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  retryButton: {
    backgroundColor: colors.red,
    paddingVertical: scale(8),
    paddingHorizontal: scale(20),
    borderRadius: scale(4),
    alignItems: 'center',
    marginBottom: scale(20),
    alignSelf: 'center',
  },
  retryButtonText: {
    color: colors.white,
    fontSize: scale(14),
    fontFamily: fonts.SFPro.medium,
  },
});

export default LoginScreen;
