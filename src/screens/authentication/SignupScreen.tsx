import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { WrapperContainer, Dropdown } from 'components';
import { scale } from 'utils/device';
import { colors, fonts } from 'assets';
import { useDispatch, useSelector } from 'react-redux';
import { userActions } from 'store';
import { RootState } from 'store/types';
import { useGlobalContext } from 'context/GlobalContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { network } from 'api';
import { CommonActions } from '@react-navigation/native';

interface Props {
  navigation: any;
}

interface SignupInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
  secureTextEntry?: boolean;
  isDropdown?: boolean;
  dropdownData?: any[];
  onSelect?: (value: any) => void;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  zIndex?: number;
  valueKey?: string;
  labelKey?: string;
}

const SignupInput: React.FC<SignupInputProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
  secureTextEntry,
  isDropdown,
  dropdownData,
  onSelect,
  keyboardType = 'default',
  zIndex,
  valueKey = 'code',
}) => {
  return (
    <View style={styles.inputWrapper}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.phoneInputContainer}>
        {isDropdown ? (
          <Dropdown
            data={dropdownData || []}
            selectedValue={value}
            onSelect={item => onSelect && onSelect(item[valueKey])}
            labelKey="code_name"
            zIndex={zIndex}
          />
        ) : (
          <TextInput
            style={styles.input}
            placeholder={placeholder}
            placeholderTextColor="#666"
            value={value}
            onChangeText={onChangeText}
            secureTextEntry={secureTextEntry}
            keyboardType={keyboardType}
          />
        )}
      </View>
    </View>
  );
};
interface SignupFormProps {
  selectedCountryCode: string;
  username: string;
  email: string;
  password: string;
  onCountryCodeChange: (code: string) => void;
  onUsernameChange: (text: string) => void;
  onEmailChange: (text: string) => void;
  onPasswordChange: (text: string) => void;
  onRegister: () => void;
  isLoading: boolean;
  countryList: any[];
}

const SignupForm: React.FC<SignupFormProps> = ({
  selectedCountryCode,
  username,
  email,
  password,
  onCountryCodeChange,
  onUsernameChange,
  onEmailChange,
  onPasswordChange,
  onRegister,
  isLoading,
  countryList,
}) => {
  return (
    <>
      <View style={styles.inputContainer}>
        <SignupInput
          label="Country Code"
          value={selectedCountryCode}
          onChangeText={() => { }}
          placeholder=""
          isDropdown
          onSelect={onCountryCodeChange}
          dropdownData={countryList}
          zIndex={2000}
        />
        <SignupInput
          label="Username"
          value={username}
          onChangeText={onUsernameChange}
          placeholder="Enter Username"
        />
        <SignupInput
          label="Email"
          value={email}
          onChangeText={onEmailChange}
          placeholder="Enter Email"
          keyboardType="email-address"
        />
        <SignupInput
          label="Password"
          value={password}
          onChangeText={onPasswordChange}
          placeholder="Enter Password"
          secureTextEntry
        />
      </View>

      <View style={styles.termsContainer}>
        <Text style={styles.terms}>
          By registering, you agree to our{' '}
          <Text style={styles.termsLink}>privacy policy</Text>
        </Text>
      </View>
      <TouchableOpacity
        style={[styles.loginButton, isLoading && styles.disabledButton]}
        onPress={onRegister}
        disabled={isLoading}>
        <Text style={styles.loginButtonText}>Register</Text>
      </TouchableOpacity>
    </>
  );
};

const SignupScreen: React.FC<Props> = ({ navigation }) => {
  const { handleLoading, showMessage } = useGlobalContext();

  const dispatch = useDispatch();
  const dataRegister = useSelector(
    (state: RootState) => state.user.getRegister,
  );
  const dataCountry = useSelector((state: RootState) => state.user.country);
  const [selectedCountryCode, setSelectedCountryCode] = useState('');
  const [countryList, setCountryList] = useState<any[]>([]);
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  useEffect(() => {
    dispatch(userActions.getCountry());
  }, []);

  useEffect(() => {
    if (dataCountry.data) {
      setSelectedCountryCode(dataCountry.data[0]?.code);

      const dataCountryList = dataCountry.data?.map((item: any) => ({
        id: item.code,
        code: item.code,
        name: item.name,
        dial_code: item.dial_code,
        code_name: `${item.name} +${item.dial_code}`,
      }));
      setCountryList(dataCountryList);
    }
  }, [dataCountry.data]);

  const validateEmail = useCallback((item: string) => {
    return item.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/);
  }, []);

  const validatePassword = useCallback((item: string) => {
    return item.length >= 6;
  }, []);

  const registerData = useMemo(
    () => ({
      country_code: selectedCountryCode,
      login: email,
      password: password,
      ib: '',
      username: username,
    }),
    [selectedCountryCode, email, password, username],
  );

  const handleRegister = useCallback(() => {
    if (!email || !password || !username) {
      showMessage({
        message: 'Please fill all fields',
        type: 'error',
      });
      return;
    }

    if (!validateEmail(email)) {
      showMessage({
        message: 'Please enter a valid email address',
        type: 'error',
      });
      return;
    }

    if (!validatePassword(password)) {
      showMessage({
        message: 'Password must be at least 6 characters long',
        type: 'error',
      });
      return;
    }

    dispatch(userActions.getRegister(registerData));
    handleLoading(true);
  }, [
    email,
    password,
    username,
    validateEmail,
    validatePassword,
    registerData,
    dispatch,
    handleLoading,
    showMessage,
  ]);

  const handleCountryCodeChange = useCallback((code: string) => {
    setSelectedCountryCode(code);
  }, []);

  const handleUsernameChange = useCallback((text: string) => {
    setUsername(text);
  }, []);

  const handleEmailChange = useCallback((text: string) => {
    setEmail(text);
  }, []);

  const handlePasswordChange = useCallback((text: string) => {
    setPassword(text);
  }, []);

  useEffect(() => {
    handleLoading(false);
    console.log('firstLog dataRegister', dataRegister);

    if (dataRegister.success) {
      AsyncStorage.setItem('ACCESS_TOKEN', dataRegister.data?.token);
      dispatch(userActions.loginSuccess(dataRegister))
      network.setToken(dataRegister.data?.token);
      navigation.dispatch(
        CommonActions.reset({
          routes: [{ name: 'MainStack' }],
        }),
      );
      showMessage({
        message: 'Register successfully',
        type: 'success',

      });
    } else if (dataRegister.error) {
      setTimeout(() => {
        showMessage({
          message: dataRegister.message,
          type: 'error',
        });
      }, 500);
    }
  }, [dataRegister]);

  return (
    <WrapperContainer style={styles.wrapper}>
      <View style={styles.container}>
        <Text style={styles.title}>Register now, it's quick!</Text>

        <SignupForm
          selectedCountryCode={selectedCountryCode}
          username={username}
          email={email}
          password={password}
          onCountryCodeChange={handleCountryCodeChange}
          onUsernameChange={handleUsernameChange}
          onEmailChange={handleEmailChange}
          onPasswordChange={handlePasswordChange}
          onRegister={handleRegister}
          isLoading={dataRegister.loading}
          countryList={countryList}
        />
      </View>
    </WrapperContainer>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: colors.black,
  },
  container: {
    flex: 1,
    marginTop: scale(120), // temporary
  },
  title: {
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
    marginBottom: scale(48),
    color: colors.white,
    paddingHorizontal: scale(20),
  },
  inputContainer: {
    marginBottom: scale(36),
    gap: scale(16),
  },
  inputWrapper: {
    borderBottomWidth: 0.2,
    borderBottomColor: `${colors.white}80`,
    paddingHorizontal: scale(20),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  phoneInputContainer: {
    justifyContent: 'center',
    gap: scale(16),
    height: scale(56),
  },
  countryCode: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
  },
  countryCodeIcon: {
    width: scale(12),
    height: scale(12),
  },
  countryCodeText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
  input: {
    flex: 1,
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
  loginButton: {
    backgroundColor: colors.yellow,
    paddingVertical: scale(8),
    paddingHorizontal: scale(30),
    borderRadius: scale(4),
    alignItems: 'center',
    marginBottom: scale(20),
    alignSelf: 'center',
  },
  disabledButton: {
    opacity: 0.7,
  },
  loginButtonText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  termsContainer: {
    marginBottom: scale(26),
    alignSelf: 'center',
  },
  terms: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
  termsLink: {
    textDecorationLine: 'underline',
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginTop: scale(10),
    paddingHorizontal: scale(20),
  },
});

export default SignupScreen;
