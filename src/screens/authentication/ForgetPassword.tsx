import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import React, {useEffect, useState, useCallback} from 'react';
import {WrapperContainer, CountryCodePicker} from 'components';
import {scale} from 'utils/device';
import {colors, fonts} from 'assets';
import {useDispatch, useSelector} from 'react-redux';
import {userActions} from 'store';
import {RootState} from 'store/types';
import {useGlobalContext} from 'context/GlobalContext';

interface Props {
  navigation: any;
}

interface CountryCode {
  id: string;
  code: string;
  name: string;
  dial_code: string;
  code_name: string;
  [key: string]: any;
}

interface ForgetPasswordInputProps {
  selectedCountryCode: string;
  onSelect: (value: CountryCode) => void;
  countryList: CountryCode[];
  value: string;
  onChangeText: (text: string) => void;
  valueKey?: string;
}

const ForgetPasswordInput: React.FC<ForgetPasswordInputProps> = ({
  selectedCountryCode,
  onSelect,
  countryList,
  value,
  onChangeText,
  valueKey = 'dial_code',
}) => {
  return (
    <View style={styles.inputWrapper}>
      <Text style={styles.label}>Phone Number</Text>
      <View style={styles.phoneInputContainer}>
        <CountryCodePicker
          selectedValue={selectedCountryCode}
          onSelect={item => onSelect && onSelect(item[valueKey])}
          data={countryList}
          labelKey="code_name"
        />
        <TextInput
          style={styles.input}
          placeholder="Enter Email"
          placeholderTextColor="#666"
          value={value}
          onChangeText={onChangeText}
        />
      </View>
    </View>
  );
};

interface ActionButtonsProps {
  onRecover: () => void;
  onSignup: () => void;
  onLogin: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onRecover,
  onSignup,
  onLogin,
}) => {
  return (
    <>
      <TouchableOpacity style={styles.loginButton} onPress={onRecover}>
        <Text style={styles.loginButtonText}>Recover</Text>
      </TouchableOpacity>
      <Text style={styles.orText}>or</Text>
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={onSignup}>
          <Text style={styles.buttonText}>Sign Up</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.button} onPress={onLogin}>
          <Text style={styles.buttonText}>Login</Text>
        </TouchableOpacity>
      </View>
    </>
  );
};

const ForgetPasswordScreen: React.FC<Props> = ({navigation}) => {
  const {showMessage} = useGlobalContext();
  const [selectedCountryCode, setSelectedCountryCode] = useState('');
  const [email, setEmail] = useState('');
  const [countryList, setCountryList] = useState<CountryCode[]>([]);
  const dispatch = useDispatch();

  const dataCountry = useSelector((state: RootState) => state.user.country);
  const dataForgotUsername = useSelector(
    (state: RootState) => state.user.forgotUsername,
  );

  useEffect(() => {
    dispatch(userActions.getCountry());
  }, []);

  useEffect(() => {
    if (dataCountry.data && Array.isArray(dataCountry.data)) {
      const dataCountryList = dataCountry.data.map((item: any) => ({
        id: item.code,
        code: item.code,
        name: item.name,
        dial_code: item.dial_code,
        code_name: `${item.code} +${item.dial_code}`,
      }));
      setCountryList(dataCountryList);
      setSelectedCountryCode(`+${dataCountryList[0].dial_code}`);
    } else if (dataCountry.loading) {
      // Show loading state
      setCountryList([
        {
          id: 'loading',
          code: 'loading',
          name: 'Loading...',
          dial_code: '',
          code_name: 'Loading countries...',
        },
      ]);
    } else if (dataCountry.error) {
      // Show error state
      setCountryList([
        {
          id: 'error',
          code: 'error',
          name: 'Error loading countries',
          dial_code: '',
          code_name: 'Error loading countries',
        },
      ]);
      showMessage({
        message: dataCountry.message || 'Failed to load countries',
        type: 'error',
      });
    }
  }, [
    dataCountry.data,
    dataCountry.loading,
    dataCountry.error,
    dataCountry.message,
    showMessage,
  ]);

  useEffect(() => {
    if (dataForgotUsername.success) {
      navigation.navigate('Login');
    }
  }, [dataForgotUsername.success, navigation]);

  const handleSignup = useCallback(() => {
    navigation.navigate('Signup');
  }, [navigation]);

  const handleLogin = useCallback(() => {
    navigation.navigate('Login');
  }, [navigation]);

  const handleRecover = useCallback(() => {
    if (!email || !selectedCountryCode) {
      showMessage({
        message: 'Please fill all fields',
        type: 'error',
      });
      return;
    }

    if (dataCountry.loading) {
      showMessage({
        message: 'Please wait for countries to load',
        type: 'error',
      });
      return;
    }

    if (dataCountry.error) {
      showMessage({
        message: 'Please retry loading countries',
        type: 'error',
      });
      return;
    }

    const data = {
      login: email,
      dial_code: selectedCountryCode,
    };
    dispatch(userActions.forgotUsername(data));
  }, [
    email,
    selectedCountryCode,
    dispatch,
    showMessage,
    dataCountry.loading,
    dataCountry.error,
  ]);

  const handleCountryCodeChange = useCallback((value: CountryCode) => {
    setSelectedCountryCode(`+${value}`);
  }, []);

  const handleEmailChange = useCallback((text: string) => {
    setEmail(text);
  }, []);

  const handleRetryCountries = useCallback(() => {
    dispatch(userActions.getCountry());
  }, [dispatch]);

  return (
    <WrapperContainer style={styles.wrapper}>
      <View style={styles.container}>
        <Text style={styles.title}>Forgot Password</Text>
        <View style={styles.descriptionContainer}>
          <Text style={styles.description}>
            Enter the email you have registered with{'\n'}
            <Text style={styles.descriptionLink}>TigerTrade</Text> and we will
            help you create a new password.
          </Text>
        </View>
        <View style={styles.inputContainer}>
          <ForgetPasswordInput
            selectedCountryCode={selectedCountryCode}
            onSelect={handleCountryCodeChange}
            countryList={countryList}
            value={email}
            onChangeText={handleEmailChange}
          />
        </View>

        {dataCountry.error && (
          <TouchableOpacity
            style={styles.retryButton}
            onPress={handleRetryCountries}>
            <Text style={styles.retryButtonText}>Retry Loading Countries</Text>
          </TouchableOpacity>
        )}

        <ActionButtons
          onRecover={handleRecover}
          onSignup={handleSignup}
          onLogin={handleLogin}
        />
      </View>
    </WrapperContainer>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: colors.black,
  },
  container: {
    flex: 1,
    marginTop: scale(120), // temporary
  },
  title: {
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
    marginBottom: scale(14),
    color: colors.white,
    paddingHorizontal: scale(20),
  },
  descriptionContainer: {
    paddingHorizontal: scale(20),
    marginBottom: scale(60),
  },
  description: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
  descriptionLink: {
    color: colors.yellow,
  },

  inputContainer: {
    marginBottom: scale(36),
    gap: scale(16),
  },
  inputWrapper: {
    borderBottomWidth: 0.2,
    borderBottomColor: `${colors.white}80`,
    paddingHorizontal: scale(20),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(16),
    height: scale(56),
  },
  countryCode: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
  },
  countryCodeIcon: {
    width: scale(12),
    height: scale(12),
  },
  countryCodeText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
  input: {
    flex: 1,
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
  loginButton: {
    backgroundColor: colors.yellow,
    paddingVertical: scale(8),
    paddingHorizontal: scale(30),
    borderRadius: scale(4),
    alignItems: 'center',
    marginBottom: scale(20),
    alignSelf: 'center',
  },
  loginButtonText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  orText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    textAlign: 'center',
    marginBottom: scale(28),
    marginTop: scale(20),
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: scale(16),
  },
  button: {
    width: scale(100),
    backgroundColor: colors.gray_button,
    paddingVertical: scale(8),
    borderRadius: scale(4),
    borderWidth: 0.2,
    borderColor: `${colors.gray_border}80`,
  },
  buttonText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors.red,
    paddingVertical: scale(8),
    paddingHorizontal: scale(20),
    borderRadius: scale(4),
    alignItems: 'center',
    marginBottom: scale(20),
    alignSelf: 'center',
  },
  retryButtonText: {
    color: colors.white,
    fontSize: scale(14),
    fontFamily: fonts.SFPro.medium,
  },
});

export default ForgetPasswordScreen;
