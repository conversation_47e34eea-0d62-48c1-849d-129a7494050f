import {colors, fonts, images} from 'assets';
import React, {useState} from 'react';
import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {scale} from 'utils/device';

function TechnicalAnalysis() {
  const [selected, setSelected] = useState({
    title: 'FOREX',
    value: 'forex',
  });

  const listBtn = [
    {
      title: 'FOREX',
      value: 'forex',
    },
    {
      title: 'STOCK',
      value: 'stock',
    },
    {
      title: 'INDICES',
      value: 'indices',
    },
    {
      title: 'COMMODITIES',
      value: 'commodities',
    },
  ];

  const data = [
    {
      image: images.tools_image,
      title: '<PERSON> (CME) (M3) Intraday: under pressure.',
      description: '(F4) Intraday: key resistance at 70.75',
      date: '2023/12/07  7:00 AM',
      type: 'negative',
    },
    {
      image: images.tools_image,
      title: '<PERSON> (CME) (M3) Intraday: under pressure.',
      description: 'Intraday: bullish bias above 1.3480.',
      date: '2023/12/07  7:00 AM',
      type: 'positive',
    },
  ];

  const renderItem = ({item}) => (
    <TouchableOpacity style={{marginTop: scale(17)}}>
      <Image source={images.tools_image} style={styles.itemImage} />
      <Text style={[styles.itemInfo, {marginTop: scale(10)}]}>
        {item?.title}
      </Text>
      <Text
        style={[
          styles.itemInfo,
          {
            color: item?.type === 'positive' ? colors.green : colors.red,
            marginTop: scale(4),
          },
        ]}>
        {item?.description}
      </Text>
      <Text
        style={[
          styles.itemInfo,
          {
            color: colors.gray_text,
            marginTop: scale(4),
          },
        ]}>
        {item?.date}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View>
      <View style={styles.listBtnWrapper}>
        {listBtn.map(item => (
          <TouchableOpacity
            onPress={() => setSelected(item)}
            style={styles.listBtn}>
            <Text
              style={{
                fontFamily: fonts.SFPro.medium,
                fontSize: scale(15),
                color:
                  item?.value === selected?.value
                    ? colors.yellow
                    : colors.white,
              }}>
              {item?.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={data}
        renderItem={renderItem}
        style={styles.flatList}
      />
    </View>
  );
}

export default TechnicalAnalysis;

const styles = StyleSheet.create({
  listBtnWrapper: {
    flexDirection: 'row',
    backgroundColor: colors.black2,
    alignItems: 'center',
    paddingTop: scale(5),
    paddingBottom: scale(9),
    paddingHorizontal: scale(20),
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: colors.grayBorder,
  },
  flatList: {
    paddingHorizontal: scale(20),
  },
  listBtn: {
    paddingVertical: scale(10),
    marginRight: scale(50),
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemImage: {
    width: scale(400),
    height: scale(150),
    borderRadius: scale(5),
  },
  itemInfo: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    color: colors.white,
    textAlign: 'center',
  },
});
