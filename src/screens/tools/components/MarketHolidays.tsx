import {colors, fonts} from 'assets';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import React, {useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {Calendar, CalendarList, LocaleConfig} from 'react-native-calendars';
import {scale} from 'utils/device';

LocaleConfig.locales['en'] = {
  monthNames: [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ],
  monthNamesShort: [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],
  dayNames: [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ],
  dayNamesShort: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],
  today: 'Today',
};

LocaleConfig.defaultLocale = 'en';

function MarketHolidays() {
  //default is today
  const [selected, setSelected] = useState<string>(
    new Date().toISOString().split('T')[0],
  );

  const {openBottomSheet} = useBottomSheet();

  const onPressDate = date => {
    // openBottomSheet({
    //   children: _renderBottomSheetContent(date),
    //   showCloseBtn: true,
    //   showBackBtn: true,
    // });
  };

  const _renderBottomSheetContent = date => <View></View>;

  const renderCustomHeader = (date: any) => {
    const header = date.toString('MMMM yyyy');
    const [month, year] = header.split(' ');

    return (
      <View style={styles.headerView}>
        <Text style={styles.headerText}>{`${month}`} </Text>
        <Text style={styles.headerText}>{year}</Text>
      </View>
    );
  };

  const renderDay = ({date, state}) => {
    if (!date) return null;
    const dayOfWeek = new Date(date.dateString).getDay(); // 0 = Sunday, 6 = Saturday
    const textColor =
      dayOfWeek === 0 || dayOfWeek === 6
        ? colors.weekend_date // Weekenddays
        : colors.gray_text; // Weekdays

    return (
      <TouchableOpacity
        onPress={() => {
          setSelected(date.dateString);
          onPressDate(date);
        }}
        style={{
          padding: scale(5),
        }}>
        <View
          style={{
            borderRadius: scale(4),
            borderColor: '#616161',
            borderWidth: date.dateString === selected ? 1 : 0,
            padding: scale(5),
          }}>
          <Text
            style={{
              fontFamily: fonts.SFPro,
              fontSize: scale(11),
              color: textColor,
            }}>
            {date.day}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View>
      <Calendar
        hideExtraDays={true}
        renderHeader={renderCustomHeader}
        style={{backgroundColor: colors.black2}}
        firstDay={1}
        renderArrow={() => <></>}
        dayComponent={renderDay}
        theme={{
          'stylesheet.calendar.header': {
            dayTextAtIndex5: {
              color: colors.weekend_date,
            },
            dayTextAtIndex6: {
              color: colors.weekend_date,
            },
          },
          calendarBackground: colors.black2,
          dayTextColor: colors.white,
        }}
      />
    </View>
  );
}

export default MarketHolidays;

const styles = StyleSheet.create({
  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: scale(10),
    marginBottom: scale(10),
  },
  headerText: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(11),
    color: colors.gray_text,
  },
});
