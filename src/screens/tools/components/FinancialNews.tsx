import {useNavigation} from '@react-navigation/native';
import {colors, fonts, images} from 'assets';
import React from 'react';
import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {scale} from 'utils/device';

function FinancialNews() {
  const navigation = useNavigation<any>();
  const newList = [
    {
      image: images.logo_news,
      title: 'EUR/USD extends winning spell ahead of US NFP, Services PMI.',
      date: '2024/05/03 9:53 AM',
    },
    {
      image: images.logo_news,
      title: 'EUR/USD extends winning spell ahead of US NFP, Services PMI.',
      date: '2024/05/03 9:53 AM',
    },
  ];

  const onPressNewsItem = item => {
    navigation.navigate('NewsDetailScreen', {data: item});
  };

  const renderNewsItem = ({item, index}) => (
    <TouchableOpacity
      onPress={() => onPressNewsItem(item)}
      style={[
        styles.newsItem,
        {
          borderBottomWidth: index === newList?.length - 1 ? 1 : 0,
        },
      ]}>
      <Image source={item?.image} style={styles.newsImage} />
      <View style={{flex: 1, paddingHorizontal: scale(10)}}>
        <Text style={styles.title}>{item?.title}</Text>
        <Text style={styles.date}>{item?.date}</Text>
      </View>
    </TouchableOpacity>
  );
  return (
    <FlatList
      data={newList}
      renderItem={renderNewsItem}
      ListFooterComponent={() => (
        <TouchableOpacity style={styles.loadMoreBtn}>
          <Text style={styles.loadMoreTxt}>Load More</Text>
        </TouchableOpacity>
      )}
    />
  );
}

export default FinancialNews;

const styles = StyleSheet.create({
  newsItem: {
    flexDirection: 'row',
    paddingVertical: scale(16),
    paddingHorizontal: scale(20),
    borderTopWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  newsImage: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(25),
  },
  loadMoreBtn: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.black,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(5),
    marginTop: scale(20),
  },
  loadMoreTxt: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
  },
  title: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
  },
  date: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(13),
    color: colors.white,
    marginTop: scale(7),
  },
});
