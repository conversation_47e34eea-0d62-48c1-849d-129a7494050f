import { colors, fonts, images } from 'assets';
import React, { useEffect, useState } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { Cell, Row, Table, TableWrapper } from 'react-native-reanimated-table';
import Spinner from 'react-native-spinkit';
import WebView from 'react-native-webview';
import { scale } from 'utils/device';

function EconomicCalendar() {
  return (
    <WebView
      source={{ uri: "https://www.tradays.com/en/economic-calendar/widget?mode=2&utm_source=secure.smiletradefx.com" }}
      style={{ marginTop: scale(20), backgroundColor: colors.black2 }}
      javaScriptEnabled={true}
      domStorageEnabled={true}
      originWhitelist={['*']}
      bounces={false}
      startInLoadingState={true}
      renderLoading={() => (
        <View style={styles.loadingContainer}>
          <Spinner
            isVisible={true}
            size={50}
            type={'FadingCircleAlt'}
            color={colors.white}
          />
        </View>
      )}
    />
  );
}

export default EconomicCalendar;

const styles = StyleSheet.create({
  tableHeaderWrapper: {
    paddingTop: scale(8),
    paddingBottom: scale(13),
    paddingHorizontal: scale(20),
    borderColor: colors.grayBorder,
    borderBottomWidth: 1,
  },
  tableHeader: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(13),
    color: `${colors.white}60`,
  },
  tableRow: {
    flexDirection: 'row',
    paddingTop: scale(9),
    paddingBottom: scale(13),
    paddingHorizontal: scale(20),
  },
  tableDataTxt: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(13),
    color: `${colors.white}60`,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: colors.black,
  },
});
