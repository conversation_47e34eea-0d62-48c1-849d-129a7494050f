import {useIsFocused} from '@react-navigation/native';
import {colors, fonts, images} from 'assets';
import {<PERSON><PERSON>, WrapperContainer} from 'components';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {screenFocusActions} from 'store';
import {scale} from 'utils/device';
import EconomicCalendar from './components/EconomicCalendar';
import MarketHolidays from './components/MarketHolidays';
import TechnicalAnalysis from './components/TechnicalAnalysis';
import FinancialNews from './components/FinancialNews';

function ToolsScreen() {
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (isFocused) {
      dispatch(screenFocusActions.setCurrentScreen('ToolsScreen'));
    }
  }, [isFocused]);

  const [selectedTools, setSelectedTools] = useState({
    title: 'Financial News',
    value: 0,
  });

  const list = [
    {
      title: 'Financial News',
      value: 0,
    },
    {
      title: 'Economic Calendar',
      value: 1,
    },
    {
      title: 'Technical Analysis',
      value: 2,
    },
    {
      title: 'Market Holidays',
      value: 3,
    },
  ];

  useEffect(() => {
    switch (selectedTools?.value) {
      case 0:
        return scrollViewRef?.current?.scrollTo({x: 0, animated: true});
      case 1:
        return scrollViewRef?.current?.scrollTo({
          x: scale(100),
          animated: true,
        });
      case 2:
        return scrollViewRef?.current?.scrollTo({
          x: scale(200),
          animated: true,
        });
      case 3:
        return scrollViewRef?.current?.scrollTo({
          x: scale(300),
          animated: true,
        });
    }
  }, [selectedTools?.value]);

  return (
    <WrapperContainer style={{backgroundColor: colors.black2}}>
      <Header />
      <View style={styles.titleRow}>
        <Text style={styles.screenTitle}>Tools</Text>
        <TouchableOpacity style={styles.searchBtn}>
          <Image source={images.ic_search} style={styles.icSearch} />
        </TouchableOpacity>
      </View>
      <View>
        <ScrollView
          ref={scrollViewRef}
          horizontal
          bounces={false}
          showsHorizontalScrollIndicator={false}
          style={styles.listBtnRow}>
          {list.map(item => (
            <TouchableOpacity
              onPress={() => setSelectedTools(item)}
              style={styles.listBtn}>
              <Text style={styles.listBtnText}>{item?.title}</Text>
              {item?.value === selectedTools?.value && (
                <View style={styles.dot} />
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      <View style={{flex: 1}}>
        {selectedTools?.value === 0 && <FinancialNews />}
        {selectedTools?.value === 1 && <EconomicCalendar />}
        {selectedTools?.value === 2 && <TechnicalAnalysis />}
        {selectedTools?.value === 3 && <MarketHolidays />}
      </View>
    </WrapperContainer>
  );
}

export default ToolsScreen;

const styles = StyleSheet.create({
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: scale(35),
    justifyContent: 'space-between',
    paddingHorizontal: scale(20),
  },
  screenTitle: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(30),
    color: colors.white,
  },
  searchBtn: {
    width: scale(40),
    height: scale(40),
    backgroundColor: colors.black2,
    borderWidth: 1,
    borderColor: colors.gray_button,
    alignItems: 'center',
    justifyContent: 'center',
  },
  icSearch: {
    width: scale(24),
    height: scale(24),
  },
  listBtnRow: {
    marginTop: scale(40),
    paddingLeft: scale(20),
  },
  listBtn: {
    marginRight: scale(16),
    paddingVertical: scale(10),
  },
  listBtnText: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    color: colors.white,
  },
  dot: {
    width: scale(5),
    height: scale(5),
    borderRadius: scale(5),
    backgroundColor: colors.yellow,
    alignSelf: 'center',
    marginTop: scale(3),
  },
});
