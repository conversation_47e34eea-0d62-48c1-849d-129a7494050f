import { useNavigation, useRoute } from '@react-navigation/native';
import { colors, fonts, images } from 'assets';
import { WrapperContainer } from 'components';
import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Spinner from 'react-native-spinkit';
import WebView from 'react-native-webview';
import { scale } from 'utils/device';

function NewsDetailScreen() {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const { data } = route.params;

  const htmlContent = `
  <html>
    <head>
      <style>
        body {
          background-color: ${colors.white}; 
          height: 100%;
          margin: 0;
          padding: 0;
        }
        .content-wrapper {
          background-color: ${colors.white};
          text-align: center;
        }
       
      </style>
    </head>
    <body>
      <div class="content-wrapper">
        ${data.content}
      </div>
    </body>
  </html>
`;

  const onPressBack = () => {
    navigation.goBack();
  };

  return (
    <WrapperContainer style={styles.container}>
      <View>
        <TouchableOpacity onPress={onPressBack} style={styles.backButton}>
          <Image source={images.ic_back} style={styles.iconBack} />
          <Text style={styles.back}>Back</Text>
        </TouchableOpacity>
      </View>
      <WebView
        source={{ html: htmlContent }}
        style={{ marginTop: scale(20), backgroundColor: colors.black, flex: 1, }}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        originWhitelist={['*']}
        bounces={false}
        startInLoadingState={true}
        renderLoading={() => (
          <View style={styles.loadingContainer}>
            <Spinner
              isVisible={true}
              size={50}
              type={'FadingCircleAlt'}
              color={colors.white}
              style={{ marginTop: scale(-100) }}
            />
          </View>
        )}
      />
    </WrapperContainer>
  );
}

export default NewsDetailScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    // left: scale(20),
    marginLeft: scale(20),
    marginTop: scale(20),
    // top: scale(20),
  },
  iconBack: {
    width: scale(24),
    height: scale(24),
  },
  back: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    color: colors.white,
    marginLeft: scale(10),
    zIndex: 99999,
  },
  title: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(25),
    color: colors.white,
  },
  date: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(13),
    color: colors.gray_text,
    marginTop: scale(20),
  },
  content: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.white,
    marginTop: scale(25),
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: colors.black,
  },
});
