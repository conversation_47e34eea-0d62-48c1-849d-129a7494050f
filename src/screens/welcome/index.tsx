import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import React from 'react';
import { WrapperContainer } from 'components';
import { scale } from 'utils/device';
import { colors, fonts } from 'assets';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const WelcomeScreen = () => {
  const navigation = useNavigation<any>();

  const saveIsFirstLaunch = async () => {
    await AsyncStorage.setItem('isFirst', 'true');
  };

  const handleLogin = () => {
    navigation.navigate('Login');
    saveIsFirstLaunch();
  };

  const handleSignup = () => {
    navigation.navigate('Signup');
    saveIsFirstLaunch();
  };

  const handleForgetPassword = () => {
    navigation.navigate('ForgetPassword');
    saveIsFirstLaunch();
  };



  return (
    <WrapperContainer style={styles.wrapper}>
      <View style={styles.container}>
        <Text style={styles.welcomeTitle}>Welcome to</Text>
        <Text style={styles.brandName}>ESmiltradefx</Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.signupButton} onPress={handleSignup}>
            <Text style={styles.signupButtonText}>Sign Up</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
            <Text style={styles.loginButtonText}>Log In</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.forgotPasswordButton}
          onPress={handleForgetPassword}>
          <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
        </TouchableOpacity>
      </View>
    </WrapperContainer>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: colors.black,
  },
  container: {
    flex: 1,
    marginTop: scale(70),
    borderTopLeftRadius: scale(20),
    borderTopRightRadius: scale(20),
    alignItems: 'center',
    paddingTop: scale(172),
  },
  welcomeTitle: {
    color: 'white',
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
  },
  brandName: {
    color: colors.yellow,
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
    marginTop: scale(8),
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: scale(47),
    gap: scale(16),
  },
  signupButton: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.gray_button,
    borderRadius: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0.2,
    borderColor: `${colors.gray_border}80`,
  },
  signupButtonText: {
    color: 'white',
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  loginButton: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.yellow,
    borderRadius: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginButtonText: {
    color: colors.black,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  forgotPasswordButton: {
    position: 'absolute',
    bottom: scale(54),
  },
  forgotPasswordText: {
    color: 'white',
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    opacity: 0.9,
  },
});

export default WelcomeScreen;
