import React, {memo} from 'react';
import {Image, StyleSheet, TouchableOpacity} from 'react-native';
import {images} from 'assets';
import {scale} from 'utils/device';

interface SwitchProps {
  value: boolean;
  onValueChange?: (value: boolean) => void;
  disabled?: boolean;
}

const Switch = ({value, onValueChange, disabled = false}: SwitchProps) => {
  const handlePress = () => {
    if (!disabled && onValueChange) {
      onValueChange(!value);
    }
  };

  return (
    <TouchableOpacity activeOpacity={1} onPress={handlePress}>
      <Image
        source={value ? images.ic_switch_active : images.ic_switch}
        style={styles.iconSwitch}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  iconSwitch: {
    width: scale(24),
    height: scale(24),
  },
});

export default memo(Switch);
