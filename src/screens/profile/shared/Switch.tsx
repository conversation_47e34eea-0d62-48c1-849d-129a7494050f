import React, {memo} from 'react';
import {Image, StyleSheet, TouchableOpacity} from 'react-native';
import {images} from 'assets';
import {scale} from 'utils/device';

interface SwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
}

const Switch = ({value, onValueChange}: SwitchProps) => {
  return (
    <TouchableOpacity activeOpacity={0.6} onPress={() => onValueChange(!value)}>
      <Image
        source={value ? images.ic_switch_active : images.ic_switch}
        style={styles.iconSwitch}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  iconSwitch: {
    width: scale(24),
    height: scale(24),
  },
});

export default memo(Switch);
