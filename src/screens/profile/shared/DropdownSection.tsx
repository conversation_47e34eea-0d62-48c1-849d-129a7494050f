import {View, Text, StyleSheet} from 'react-native';
import React, {memo} from 'react';
import {Dropdown} from 'components';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';

interface DropdownSectionProps {
  label: string;
  value: string;
  data: Array<{id: string; name: string}>;
  onSelect: (item: any) => void;
  zIndex: number;
  renderSeparator: React.ReactNode;
  isYellow?: boolean;
}

const DropdownSection = memo(
  ({
    label,
    value,
    data,
    onSelect,
    zIndex,
    renderSeparator,
    isYellow,
  }: DropdownSectionProps) => (
    <View style={styles.inputSection}>
      <View style={styles.inputContent}>
        <Text style={styles.label}>{label}</Text>
        <Dropdown
          data={data}
          selectedValue={value}
          onSelect={onSelect}
          zIndex={zIndex}
          isYellow={isYellow}
        />
      </View>
      {renderSeparator}
    </View>
  ),
);

const styles = StyleSheet.create({
  inputSection: {
    height: scale(70),
    paddingTop: scale(10),
  },
  inputContent: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: scale(16),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
});

export default memo(DropdownSection);
