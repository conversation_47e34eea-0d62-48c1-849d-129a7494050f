import {View, Text, TextInput, StyleSheet, TextInputProps} from 'react-native';
import React, {ReactNode, memo} from 'react';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';

interface TextInputCustomProps extends TextInputProps {
  label?: string;
  rightIcon?: ReactNode;
}

const TextInputCustom = ({
  label,
  rightIcon,
  style,
  ...props
}: TextInputCustomProps) => {
  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      <View style={styles.inputContainer}>
        <TextInput
          style={[styles.input, style]}
          placeholderTextColor={colors.gray_border}
          {...props}
        />
        {rightIcon && (
          <View style={styles.rightIconContainer}>{rightIcon}</View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingHorizontal: scale(16),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: scale(40),
  },
  input: {
    flex: 1,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    padding: 0,
    margin: 0,
  },
  rightIconContainer: {
    marginLeft: scale(8),
  },
});

export default memo(TextInputCustom);
