import {
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import React, { memo } from 'react';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';

interface ButtonProps {
  title: string;
  onPress?: () => void;
  variant?: 'primary' | 'secondary';
  style?: ViewStyle;
  textStyle?: TextStyle;
  disabled?: boolean;
}

const Button = memo(({title, onPress, variant = 'primary', style, textStyle, disabled}: ButtonProps) => {
  const buttonStyle = [
    styles.button,
    variant === 'primary' && styles.primaryButton,
    variant === 'secondary' && styles.secondaryButton,
    disabled && styles.disabledButton,
      style,
    ];

    const titleStyle = [
      styles.title,
      variant === 'primary' && styles.primaryTitle,
      variant === 'secondary' && styles.secondaryTitle,
      disabled && styles.disabledTitle,
      textStyle,
    ];

    return (
      <TouchableOpacity
        style={buttonStyle}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}>
        <Text style={titleStyle}>{title}</Text>
      </TouchableOpacity>
    );
  },
);

const styles = StyleSheet.create({
  button: {
    borderRadius: scale(5),
    alignSelf: 'center',
    paddingVertical: scale(4),
    paddingHorizontal: scale(12),
  },
  primaryButton: {
    backgroundColor: colors.yellow,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  disabledButton: {
    opacity: 0.5,
  },
  title: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    textAlign: 'center',
  },
  primaryTitle: {
    color: colors.black,
  },
  secondaryTitle: {
    color: colors.white,
  },
  disabledTitle: {
    color: colors.gray_border,
  },
});

export default memo(Button);
