import {View, Text, StyleSheet, Image} from 'react-native';
import React, {useCallback, useState, memo} from 'react';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button, Separator, Switch} from '../shared';

interface Authenticator {
  icon: any;
  title: string;
  subtitle: string;
}

const Security = () => {
  const [authenticators, setAuthenticators] = useState({
    google: true,
    phone: true,
    email: false,
  });

  const handleToggle = useCallback((key: keyof typeof authenticators) => {
    setAuthenticators(prev => ({...prev, [key]: !prev[key]}));
  }, []);

  const renderAuthenticatorItem = useCallback(
    ({icon, title, subtitle}: Authenticator) => (
      <View style={styles.authenticatorItem}>
        <View style={[styles.iconContainer]}>
          <Image source={icon} style={styles.icon} />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subtitle}>{subtitle}</Text>
        </View>
        <Switch
          value={authenticators[title as keyof typeof authenticators]}
          onValueChange={() =>
            handleToggle(title as keyof typeof authenticators)
          }
        />
      </View>
    ),
    [authenticators, handleToggle],
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Separator />
        <Text style={styles.headerText}>Two-Factor Authentication</Text>
      </View>

      {renderAuthenticatorItem({
        icon: images.ic_logo_google,
        title: 'Google Authenticator',
        subtitle: 'Used for login and security modifications',
      })}

      {renderAuthenticatorItem({
        icon: images.ic_phone_authenticator,
        title: 'Phone Authenticator',
        subtitle: '+85 21XXXX30',
      })}

      {renderAuthenticatorItem({
        icon: images.ic_email_authenticator,
        title: 'Email Authenticator',
        subtitle: '<EMAIL>',
      })}

      <View style={styles.deleteButtonContainer}>
        <Button
          title="Delete Account"
          onPress={() => {}}
          style={styles.deleteButton}
          textStyle={styles.deleteText}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: scale(16),
  },
  header: {
    gap: scale(15),
  },
  headerText: {
    fontSize: scale(20),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    paddingHorizontal: scale(16),
  },
  separator: {
    height: 1,
    backgroundColor: colors.white,
    opacity: 0.1,
  },
  authenticatorItem: {
    height: scale(100),
    backgroundColor: colors.black,
    borderRadius: scale(10),
    borderWidth: 1,
    borderColor: colors.gray_border,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    marginHorizontal: scale(16),
  },
  iconContainer: {
    width: scale(50),
    height: scale(50),
    justifyContent: 'center',
    alignItems: 'center',
  },
  inactiveIcon: {
    opacity: 0.6,
  },
  icon: {
    width: scale(50),
    height: scale(50),
  },
  textContainer: {
    flex: 1,
    marginLeft: scale(14),
  },
  title: {
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    opacity: 0.9,
  },
  subtitle: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    opacity: 0.9,
    marginTop: scale(2),
  },
  deleteButtonContainer: {
    marginTop: scale(40),
  },
  deleteButton: {
    backgroundColor: colors.black,
  },
  deleteText: {
    color: colors.yellow,
  },
});

export default memo(Security);
