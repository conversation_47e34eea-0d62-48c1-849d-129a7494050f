import {View, Text, StyleSheet, Image} from 'react-native';
import React, {useCallback, useState, memo} from 'react';
import {useSelector} from 'react-redux';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button, Separator, Switch} from '../shared';
import {selectProfileInfo} from 'store/reducers/profile/selector';

const Security = () => {
  const profileInfo = useSelector(selectProfileInfo);

  const [googleAuthEnabled, setGoogleAuthEnabled] = useState(true);

  const handleGoogleAuthToggle = useCallback(() => {
    setGoogleAuthEnabled(prev => !prev);
  }, []);

  const renderAuthenticatorItem = useCallback(
    ({
      icon,
      title,
      subtitle,
      isEnabled,
      onToggle,
      isEditable = false,
    }: {
      icon: any;
      title: string;
      subtitle: string;
      isEnabled: boolean;
      onToggle?: () => void;
      isEditable?: boolean;
    }) => (
      <View style={styles.authenticatorItem}>
        <View style={[styles.iconContainer]}>
          <Image source={icon} style={styles.icon} />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subtitle}>{subtitle}</Text>
        </View>
        <Switch
          value={isEnabled}
          onValueChange={isEditable ? onToggle : undefined}
          disabled={!isEditable}
        />
      </View>
    ),
    [],
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Separator />
        <Text style={styles.headerText}>Two-Factor Authentication</Text>
      </View>

      {renderAuthenticatorItem({
        icon: images.ic_logo_google,
        title: 'Google Authenticator',
        subtitle: 'Used for login and security modifications',
        isEnabled: googleAuthEnabled,
        onToggle: handleGoogleAuthToggle,
        isEditable: true,
      })}

      {renderAuthenticatorItem({
        icon: images.ic_phone_authenticator,
        title: 'Phone Authenticator',
        subtitle: profileInfo.data?.phone || '',
        isEnabled: profileInfo.data?.has_verified_phone || false,
        isEditable: false,
      })}

      {renderAuthenticatorItem({
        icon: images.ic_email_authenticator,
        title: 'Email Authenticator',
        subtitle: profileInfo.data?.email || '',
        isEnabled: profileInfo.data?.has_verified_email || false,
        isEditable: false,
      })}

      <View style={styles.deleteButtonContainer}>
        <Button
          title="Delete Account"
          onPress={() => {}}
          style={styles.deleteButton}
          textStyle={styles.deleteText}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: scale(16),
  },
  header: {
    gap: scale(15),
  },
  headerText: {
    fontSize: scale(20),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    paddingHorizontal: scale(16),
  },
  separator: {
    height: 1,
    backgroundColor: colors.white,
    opacity: 0.1,
  },
  authenticatorItem: {
    height: scale(100),
    backgroundColor: colors.black,
    borderRadius: scale(10),
    borderWidth: 1,
    borderColor: colors.gray_border,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    marginHorizontal: scale(16),
  },
  iconContainer: {
    width: scale(50),
    height: scale(50),
    justifyContent: 'center',
    alignItems: 'center',
  },
  inactiveIcon: {
    opacity: 0.6,
  },
  icon: {
    width: scale(50),
    height: scale(50),
  },
  textContainer: {
    flex: 1,
    marginLeft: scale(14),
  },
  title: {
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    opacity: 0.9,
  },
  subtitle: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    opacity: 0.9,
    marginTop: scale(2),
  },
  deleteButtonContainer: {
    marginTop: scale(40),
  },
  deleteButton: {
    backgroundColor: colors.black,
  },
  deleteText: {
    color: colors.yellow,
  },
});

export default memo(Security);
