import React, {useEffect, useCallback, memo} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image, ActivityIndicator} from 'react-native';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Separator} from '../../shared';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import DocumentDetail from './DocumentDetail';
import {useDispatch, useSelector} from 'react-redux';
import {profileActions} from 'store';
import {selectLegalDocuments} from 'store/reducers/profile/selector';

interface DocumentItemProps {
  title: string;
  onPress: () => void;
}

interface DocumentData {
  name: string;
  icon: string;
  url: string;
}

const DocumentItem = memo(({title, onPress}: DocumentItemProps) => (
  <View style={styles.documentItemContainer}>
    <TouchableOpacity style={styles.documentItem} onPress={onPress}>
      <Text style={styles.documentTitle}>{title}</Text>
      <Image source={images.ic_pdf_file} style={styles.pdfIcon} />
    </TouchableOpacity>
    <Separator />
  </View>
));

const LegalDocuments = () => {
  const dispatch = useDispatch();
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const legalDocumentsState = useSelector(selectLegalDocuments);
  const {loading, data: documents, error} = legalDocumentsState;

  useEffect(() => {
    dispatch(profileActions.getLegalDocuments());
  }, [dispatch]);

  const handleRetry = useCallback(() => {
    dispatch(profileActions.getLegalDocuments());
  }, [dispatch]);

  const handleDocumentPress = useCallback(
    (document: DocumentData) => {
      openBottomSheet({
        children: <DocumentDetail document={document} onClose={closeBottomSheet} />,
        showCloseBtn: true,
        enablePanDownToClose: true,
      });
    },
    [openBottomSheet, closeBottomSheet],
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={colors.yellow} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>Failed to load legal documents</Text>
        <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Separator />
      {documents && documents.map((document, index: number) => (
        <DocumentItem
          key={index}
          title={document.name}
          onPress={() => handleDocumentPress(document)}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: scale(40),
  },
  documentItemContainer: {
    width: '100%',
  },
  documentItem: {
    height: scale(60),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: scale(20),
  },
  documentTitle: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    opacity: 0.9,
  },
  pdfIcon: {
    width: scale(25),
    height: scale(25),
    opacity: 0.6,
  },
  errorText: {
    fontSize: scale(16),
    fontFamily: fonts.SFPro.medium,
    color: colors.red,
    marginBottom: scale(20),
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: scale(10),
    paddingHorizontal: scale(20),
    backgroundColor: colors.gray1,
    borderRadius: scale(8),
  },
  retryText: {
    fontSize: scale(14),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
});

export default memo(LegalDocuments);
