import React, {memo, useCallback} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image, Linking} from 'react-native';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button} from '../../shared';

interface DocumentDetailProps {
  document: {
    name: string;
    url: string;
    icon: string;
  };
  onClose: () => void;
}

const DocumentDetail = ({document, onClose}: DocumentDetailProps) => {
  const handleOpenDocument = useCallback(async () => {
    try {
      const canOpen = await Linking.canOpenURL(document.url);
      if (canOpen) {
        await Linking.openURL(document.url);
      } else {
        console.error('Cannot open URL:', document.url);
      }
    } catch (error) {
      console.error('Error opening URL:', error);
    }
  }, [document.url]);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{document.name}</Text>
      </View>
      
      <View style={styles.content}>
        <Image 
          source={{uri: document.icon}} 
          style={styles.documentIcon} 
          defaultSource={images.ic_pdf_file}
        />
        
        <Text style={styles.description}>
          This document contains important information about our services and policies.
          Please read it carefully before proceeding.
        </Text>
      </View>
      
      <View style={styles.buttonContainer}>
        <Button 
          title="View Document" 
          onPress={handleOpenDocument} 
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: scale(60),
    paddingHorizontal: scale(20),
  },
  header: {
    marginBottom: scale(30),
    alignItems: 'center',
  },
  title: {
    fontSize: scale(22),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    textAlign: 'center',
  },
  content: {
    alignItems: 'center',
    marginBottom: scale(40),
  },
  documentIcon: {
    width: scale(80),
    height: scale(80),
    marginBottom: scale(20),
  },
  description: {
    fontSize: scale(16),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    opacity: 0.8,
    textAlign: 'center',
    lineHeight: scale(24),
  },
  buttonContainer: {
    marginTop: scale(20),
  },
});

export default memo(DocumentDetail);
