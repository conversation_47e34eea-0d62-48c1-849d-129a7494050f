import {View, Text, StyleSheet, TextInput} from 'react-native';
import React, {useState, useMemo, memo} from 'react';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {DropdownSection, Separator, Button} from '../../shared';

interface BankData {
  country: string;
  bankName: string;
  bankBranch: string;
  accountHolder: string;
  accountNumber: string;
  swiftBic: string;
  currency: string;
}

interface BankDetailsProps {
  bank?: BankData;
  onSubmit?: () => void;
}

const BankDetails = ({bank, onSubmit}: BankDetailsProps) => {
  const [country, setCountry] = useState(bank?.country || 'All');
  const [bankName, setBankName] = useState(bank?.bankName || 'All');
  const [bankBranch, setBankBranch] = useState(bank?.bankBranch || '');
  const [accountHolder, setAccountHolder] = useState(bank?.accountHolder || '');
  const [accountNumber, setAccountNumber] = useState(bank?.accountNumber || '');
  const [swiftBic, setSwiftBic] = useState(bank?.swiftBic || '');
  const [currency, setCurrency] = useState(bank?.currency || 'All');

  const isCreate = !bank;

  const countries = useMemo(() => [{id: '1', name: 'All'}], []);
  const banks = useMemo(() => [{id: '1', name: 'All'}], []);
  const currencies = useMemo(() => [{id: '1', name: 'All'}], []);

  const renderHeader = useMemo(
    () => (
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>
          {isCreate ? 'Create New Bank' : 'Update Bank Account'}
        </Text>
      </View>
    ),
    [isCreate],
  );

  const renderForm = useMemo(
    () => (
      <View style={styles.formContainer}>
        <DropdownSection
          label="Country*"
          value={country}
          data={countries}
          onSelect={setCountry}
          zIndex={4000}
          isYellow={isCreate ? true : false}
          renderSeparator={<Separator />}
        />
        <DropdownSection
          label="Bank Name*"
          value={bankName}
          data={banks}
          onSelect={setBankName}
          zIndex={3000}
          isYellow={isCreate ? true : false}
          renderSeparator={<Separator />}
        />
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Bank Branch*</Text>
          <TextInput
            style={styles.input}
            value={bankBranch}
            onChangeText={setBankBranch}
            placeholder="Enter bank branch"
            placeholderTextColor={colors.gray_border}
          />
          <Separator />
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Account Holder's Name*</Text>
          <TextInput
            style={styles.input}
            value={accountHolder}
            onChangeText={setAccountHolder}
            placeholder="Enter account holder's name"
            placeholderTextColor={colors.gray_border}
          />
          <Separator />
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>IBAN/Account Number*</Text>
          <TextInput
            style={styles.input}
            value={accountNumber}
            onChangeText={setAccountNumber}
            placeholder="Enter account number"
            placeholderTextColor={colors.gray_border}
          />
          <Separator />
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>SWIFT/BIC</Text>
          <TextInput
            style={styles.input}
            value={swiftBic}
            onChangeText={setSwiftBic}
            placeholder="Enter SWIFT/BIC"
            placeholderTextColor={colors.gray_border}
          />
          <Separator />
        </View>
        <DropdownSection
          label="Currency*"
          value={currency}
          data={currencies}
          onSelect={setCurrency}
          zIndex={2000}
          isYellow={isCreate ? true : false}
          renderSeparator={<Separator />}
        />
      </View>
    ),
    [
      country,
      bankName,
      bankBranch,
      accountHolder,
      accountNumber,
      swiftBic,
      currency,
      countries,
      banks,
      currencies,
      isCreate,
    ],
  );

  const renderActionButton = useMemo(
    () => (
      <View style={styles.buttonContainer}>
        <Button title={isCreate ? 'Create' : 'Update'} onPress={onSubmit} />
      </View>
    ),
    [isCreate, onSubmit],
  );

  return (
    <View style={styles.container}>
      {renderHeader}
      {renderForm}
      {renderActionButton}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: scale(100),
  },
  headerContainer: {
    paddingBottom: scale(24),
  },
  headerTitle: {
    fontSize: scale(20),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginBottom: scale(55),
    paddingHorizontal: scale(20),
  },
  formContainer: {
    flex: 1,
  },
  inputContainer: {
    paddingHorizontal: scale(16),
    paddingTop: scale(10),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    marginBottom: scale(8),
  },
  input: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    paddingVertical: scale(8),
    height: scale(40),
  },
  buttonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: scale(48),
    marginTop: scale(36),
  },
});

export default memo(BankDetails);
