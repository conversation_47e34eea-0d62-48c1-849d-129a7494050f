import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import React, {useState, useMemo, useCallback, memo} from 'react';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {DropdownSection, Separator, Button, SuccessScreen} from '../../shared';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import BankDetails from './BankDetails';

interface BankData {
  country: string;
  bankName: string;
  bankBranch: string;
  accountHolder: string;
  accountNumber: string;
  swiftBic: string;
  currency: string;
}

interface BankItemProps {
  country: string;
  bankName: string;
  swiftBic: string;
  onPress: () => void;
}

const BankItem = memo(({country, bankName, swiftBic, onPress}: BankItemProps) => (
  <TouchableOpacity
    activeOpacity={0.8}
    style={styles.bankRow}
    onPress={onPress}>
    <Text style={styles.bankText}>{country}</Text>
      <Text style={styles.bankText}>{bankName}</Text>
      <Text style={styles.bankText}>{swiftBic}</Text>
    </TouchableOpacity>
  ),
);

const ManageBanks = () => {
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const [selectedCountry, setSelectedCountry] = useState('All');
  const [selectedBank, setSelectedBank] = useState('All');
  const [selectedCurrency, setSelectedCurrency] = useState('All');

  const countries = useMemo(() => [{id: '1', name: 'All'}], []);
  const banks = useMemo(() => [{id: '1', name: 'All'}], []);
  const currencies = useMemo(() => [{id: '1', name: 'All'}], []);

  const handleCountrySelect = useCallback((value: string) => {
    setSelectedCountry(value);
  }, []);

  const handleBankSelect = useCallback((value: string) => {
    setSelectedBank(value);
  }, []);

  const handleCurrencySelect = useCallback((value: string) => {
    setSelectedCurrency(value);
  }, []);

  const handleSuccess = useCallback(
    ({isCreate}: {isCreate: boolean}) => {
      openBottomSheet({
        children: (
          <SuccessScreen
            onClose={() => {
              closeBottomSheet();
            }}
            title={isCreate ? 'Bank Created' : 'Bank Updated'}
          />
        ),
        showCloseBtn: true,
        enablePanDownToClose: true,
      });
    },
    [openBottomSheet, closeBottomSheet],
  );

  const handleCreateBank = useCallback(() => {
    openBottomSheet({
      children: (
        <BankDetails onSubmit={() => handleSuccess({isCreate: true})} />
      ),
      showBackBtn: true,
      enablePanDownToClose: true,
    });
  }, [openBottomSheet, handleSuccess]);

  const handleBankPress = useCallback(
    (bank: BankData) => {
      openBottomSheet({
        children: (
          <BankDetails
            bank={bank}
            onSubmit={() => handleSuccess({isCreate: false})}
          />
        ),
        showBackBtn: true,
        enablePanDownToClose: true,
      });
    },
    [openBottomSheet, handleSuccess],
  );

  const renderFilterSection = useMemo(
    () => (
      <>
        <DropdownSection
          label="Country"
          value={selectedCountry}
          data={countries}
          onSelect={handleCountrySelect}
          zIndex={3000}
          renderSeparator={<Separator />}
        />
        <DropdownSection
          label="Bank Name*"
          value={selectedBank}
          data={banks}
          onSelect={handleBankSelect}
          zIndex={2000}
          renderSeparator={<Separator />}
        />
        <DropdownSection
          label="Currency*"
          value={selectedCurrency}
          data={currencies}
          onSelect={handleCurrencySelect}
          zIndex={1000}
          renderSeparator={<Separator />}
        />
      </>
    ),
    [
      selectedCountry,
      selectedBank,
      selectedCurrency,
      countries,
      banks,
      currencies,
      handleCountrySelect,
      handleBankSelect,
      handleCurrencySelect,
    ],
  );

  const renderBankTitle = useMemo(
    () => (
      <View style={styles.titleContainer}>
        <Text style={styles.title}>My Banks</Text>
      </View>
    ),
    [],
  );

  const renderTableHeader = useMemo(
    () => (
      <>
        <Separator />
        <View style={styles.tableHeader}>
          <Text style={styles.headerText}>Country</Text>
          <Text style={styles.headerText}>Bank Name</Text>
          <Text style={styles.headerText}>Swift/BIC</Text>
        </View>
        <Separator />
      </>
    ),
    [],
  );

  const renderBanks = useMemo(
    () => (
      <View style={styles.banksContainer}>
        <BankItem
          country="Cambodia"
          bankName="Camcombank"
          swiftBic="112201"
          onPress={() =>
            handleBankPress({
              country: 'Cambodia',
              bankName: 'CAMCOMBANK',
              bankBranch: 'PHNOMPENH1',
              accountHolder: 'YAIBroker',
              accountNumber: '************',
              swiftBic: '12002',
              currency: 'Thai Baht',
            })
          }
        />
      </View>
    ),
    [handleBankPress],
  );

  const renderLoadMore = useMemo(
    () => (
      <Button
        title="Load More"
        variant="secondary"
        style={styles.loadMoreButton}
      />
    ),
    [],
  );

  const renderCreateButton = useMemo(
    () => (
      <View style={styles.buttonContainer}>
        <Button title="Create New Bank Account" onPress={handleCreateBank} />
      </View>
    ),
    [handleCreateBank],
  );

  return (
    <View style={styles.container}>
      {renderFilterSection}
      {renderBankTitle}
      {renderTableHeader}
      {renderBanks}
      {renderLoadMore}
      {renderCreateButton}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleContainer: {
    marginTop: scale(24),
    marginBottom: scale(16),
    paddingHorizontal: scale(16),
  },
  title: {
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: scale(10),
    backgroundColor: colors.black2,
    paddingHorizontal: scale(16),
  },
  headerText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    width: '33%',
  },
  banksContainer: {},
  bankRow: {
    flexDirection: 'row',
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  bankText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    width: '33%',
  },
  loadMoreButton: {
    marginVertical: scale(24),
    paddingHorizontal: scale(16),
    paddingVertical: scale(6),
  },
  buttonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: scale(48),
    marginTop: scale(36),
  },
});

export default memo(ManageBanks);
