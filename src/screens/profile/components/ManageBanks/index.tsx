import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import React, {useState, useMemo, useCallback, memo, useEffect} from 'react';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {DropdownSection, Separator, Button, SuccessScreen} from '../../shared';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import BankDetails from './BankDetails';
import {useDispatch, useSelector} from 'react-redux';
import {profileActions, userActions} from 'store';
import {RootState} from 'store/reducers';

interface BankData {
  country: string;
  bankName: string;
  bankBranch: string;
  accountHolder: string;
  accountNumber: string;
  swiftBic: string;
  currency: string;
}

interface BankItemProps {
  country: string;
  bankName: string;
  swiftBic: string;
  onPress: () => void;
}

const BankItem = memo(
  ({country, bankName, swiftBic, onPress}: BankItemProps) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.bankRow}
      onPress={onPress}>
      <Text style={styles.bankText}>{country}</Text>
      <Text style={styles.bankText}>{bankName}</Text>
      <Text style={styles.bankText}>{swiftBic}</Text>
    </TouchableOpacity>
  ),
);

const ManageBanks = () => {
  const dispatch = useDispatch();
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const [selectedCountry, setSelectedCountry] = useState('All');
  const [selectedBank, setSelectedBank] = useState('All');
  const [selectedCurrency, setSelectedCurrency] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);

  // Redux selectors
  const bankAccountsData = useSelector(
    (state: RootState) => state.profile.bankAccounts,
  );
  const bankCountriesData = useSelector(
    (state: RootState) => state.profile.bankCountries,
  );
  const bankNamesData = useSelector(
    (state: RootState) => state.profile.bankNames,
  );
  const bankCurrenciesData = useSelector(
    (state: RootState) => state.profile.bankCurrencies,
  );
  const currencyData = useSelector(
    (state: RootState) => state.profile.currency,
  );
  const bankCreateAccountData = useSelector(
    (state: RootState) => state.profile.bankCreateAccount,
  );
  const countryData = useSelector((state: RootState) => state.user.country);

  // Prepare dropdown data
  const countries = useMemo(() => {
    const allOption = [{id: 'All', name: 'All'}];
    // Use Country API data first, fallback to bankCountries if needed
    if (countryData.data) {
      return [
        ...allOption,
        ...countryData.data.map((item: any) => ({
          id: item.name, // Use name as id for API filtering
          name: item.name,
        })),
      ];
    }
    if (bankCountriesData.data) {
      return [
        ...allOption,
        ...bankCountriesData.data.map(item => ({
          id: item.name, // Use name as id for API filtering
          name: item.name,
        })),
      ];
    }
    return allOption;
  }, [countryData.data, bankCountriesData.data]);

  const banks = useMemo(() => {
    const allOption = [{id: 'All', name: 'All'}];
    // Use Bank Create Account API data first, fallback to bankNames if needed
    if (bankCreateAccountData.data) {
      return [
        ...allOption,
        ...bankCreateAccountData.data.map(item => ({
          id: item.short,
          name: item.name,
        })),
      ];
    }
    if (bankNamesData.data) {
      return [
        ...allOption,
        ...bankNamesData.data.map(item => ({id: item.code, name: item.name})),
      ];
    }
    return allOption;
  }, [bankCreateAccountData.data, bankNamesData.data]);

  const currencies = useMemo(() => {
    const allOption = [{id: 'All', name: 'All'}];
    // Use Currency API data first, fallback to bankCurrencies if needed
    if (currencyData.data) {
      return [
        ...allOption,
        ...currencyData.data.map(item => ({
          id: item.code,
          name: item.name,
        })),
      ];
    }
    if (bankCurrenciesData.data) {
      return [
        ...allOption,
        ...bankCurrenciesData.data.map(item => ({
          id: item.code,
          name: item.name,
        })),
      ];
    }
    return allOption;
  }, [currencyData.data, bankCurrenciesData.data]);

  const handleCountrySelect = useCallback((item: any) => {
    setSelectedCountry(item.id);
  }, []);

  const handleBankSelect = useCallback((item: any) => {
    setSelectedBank(item.id);
  }, []);

  const handleCurrencySelect = useCallback((item: any) => {
    setSelectedCurrency(item.id);
  }, []);

  // Load filter data on component mount
  useEffect(() => {
    dispatch(userActions.getCountry()); // Load Country API
    dispatch(profileActions.getBankCountries());
    dispatch(profileActions.getBankNames());
    dispatch(profileActions.getBankCurrencies());
    dispatch(profileActions.getCurrency()); // Load Currency API
    dispatch(profileActions.getBankCreateAccount()); // Load Bank Names API
  }, [dispatch]);

  // Load bank accounts when filters change
  useEffect(() => {
    const params: any = {};
    if (selectedCountry !== 'All') {
      params.country = selectedCountry.toLowerCase();
    }
    if (selectedBank !== 'All') {
      params.bank_name = selectedBank.toLowerCase();
    }
    if (selectedCurrency !== 'All') {
      params.currency = selectedCurrency.toLowerCase();
    }
    params.page = currentPage;
    params.limit = 10;

    dispatch(profileActions.getBankAccounts(params));
  }, [dispatch, selectedCountry, selectedBank, selectedCurrency, currentPage]);

  const handleSuccess = useCallback(
    ({isCreate}: {isCreate: boolean}) => {
      // Refresh bank data
      const params: any = {};
      if (selectedCountry !== 'All') {
        params.country = selectedCountry.toLowerCase();
      }
      if (selectedBank !== 'All') {
        params.bank_name = selectedBank.toLowerCase();
      }
      if (selectedCurrency !== 'All') {
        params.currency = selectedCurrency.toLowerCase();
      }
      params.page = 1;
      params.limit = 10;
      dispatch(profileActions.getBankAccounts(params));
      setCurrentPage(1);

      openBottomSheet({
        children: (
          <SuccessScreen
            onClose={() => {
              closeBottomSheet();
            }}
            title={isCreate ? 'Bank Created' : 'Bank Updated'}
          />
        ),
        showCloseBtn: true,
        enablePanDownToClose: true,
      });
    },
    [
      openBottomSheet,
      closeBottomSheet,
      dispatch,
      selectedCountry,
      selectedBank,
      selectedCurrency,
    ],
  );

  const handleCreateBank = useCallback(() => {
    openBottomSheet({
      children: (
        <BankDetails onSubmit={() => handleSuccess({isCreate: true})} />
      ),
      showBackBtn: true,
      enablePanDownToClose: true,
    });
  }, [openBottomSheet, handleSuccess]);

  const handleBankPress = useCallback(
    (bank: BankData) => {
      openBottomSheet({
        children: (
          <BankDetails
            bank={bank}
            onSubmit={() => handleSuccess({isCreate: false})}
          />
        ),
        showBackBtn: true,
        enablePanDownToClose: true,
      });
    },
    [openBottomSheet, handleSuccess],
  );

  const renderFilterSection = useMemo(
    () => (
      <>
        <DropdownSection
          label="Country"
          value={selectedCountry}
          data={countries}
          onSelect={handleCountrySelect}
          zIndex={3000}
          renderSeparator={<Separator />}
        />
        <DropdownSection
          label="Bank Name*"
          value={selectedBank}
          data={banks}
          onSelect={handleBankSelect}
          zIndex={2000}
          renderSeparator={<Separator />}
        />
        <DropdownSection
          label="Currency*"
          value={selectedCurrency}
          data={currencies}
          onSelect={handleCurrencySelect}
          zIndex={1000}
          renderSeparator={<Separator />}
        />
      </>
    ),
    [
      selectedCountry,
      selectedBank,
      selectedCurrency,
      countries,
      banks,
      currencies,
      handleCountrySelect,
      handleBankSelect,
      handleCurrencySelect,
    ],
  );

  const renderBankTitle = useMemo(
    () => (
      <View style={styles.titleContainer}>
        <Text style={styles.title}>My Banks</Text>
      </View>
    ),
    [],
  );

  const renderTableHeader = useMemo(
    () => (
      <>
        <Separator />
        <View style={styles.tableHeader}>
          <Text style={styles.headerText}>Country</Text>
          <Text style={styles.headerText}>Bank Name</Text>
          <Text style={styles.headerText}>Swift/BIC</Text>
        </View>
        <Separator />
      </>
    ),
    [],
  );

  const renderBanks = useMemo(() => {
    if (bankAccountsData.loading) {
      return (
        <View style={styles.banksContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      );
    }

    if (bankAccountsData.error) {
      return (
        <View style={styles.banksContainer}>
          <Text style={styles.errorText}>Error loading bank accounts</Text>
        </View>
      );
    }

    if (!bankAccountsData.data || bankAccountsData.data.length === 0) {
      return (
        <View style={styles.banksContainer}>
          <Text style={styles.emptyText}>No bank accounts found</Text>
        </View>
      );
    }

    return (
      <View style={styles.banksContainer}>
        {bankAccountsData.data.map((bank: any) => (
          <BankItem
            key={bank.id}
            country={bank.country}
            bankName={bank.name}
            swiftBic={bank.swift_code}
            onPress={() =>
              handleBankPress({
                country: bank.country,
                bankName: bank.name,
                bankBranch: bank.branch,
                accountHolder: bank.holder_name,
                accountNumber: bank.account,
                swiftBic: bank.swift_code,
                currency: bank.currency,
              })
            }
          />
        ))}
      </View>
    );
  }, [bankAccountsData, handleBankPress]);

  const handleLoadMore = useCallback(() => {
    if (
      bankAccountsData.pagination &&
      currentPage < bankAccountsData.pagination.last_page
    ) {
      setCurrentPage(prev => prev + 1);
    }
  }, [bankAccountsData.pagination, currentPage]);

  const renderLoadMore = useMemo(() => {
    if (
      !bankAccountsData.pagination ||
      currentPage >= bankAccountsData.pagination.last_page
    ) {
      return null;
    }

    return (
      <Button
        title="Load More"
        variant="secondary"
        style={styles.loadMoreButton}
        onPress={handleLoadMore}
      />
    );
  }, [bankAccountsData.pagination, currentPage, handleLoadMore]);

  const renderCreateButton = useMemo(
    () => (
      <View style={styles.buttonContainer}>
        <Button title="Create New Bank Account" onPress={handleCreateBank} />
      </View>
    ),
    [handleCreateBank],
  );

  return (
    <View style={styles.container}>
      {renderFilterSection}
      {renderBankTitle}
      {renderTableHeader}
      {renderBanks}
      {renderLoadMore}
      {renderCreateButton}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleContainer: {
    marginTop: scale(24),
    marginBottom: scale(16),
    paddingHorizontal: scale(16),
  },
  title: {
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: scale(10),
    backgroundColor: colors.black2,
    paddingHorizontal: scale(16),
  },
  headerText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    width: '33%',
  },
  banksContainer: {},
  bankRow: {
    flexDirection: 'row',
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  bankText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    width: '33%',
  },
  loadMoreButton: {
    marginVertical: scale(24),
    paddingHorizontal: scale(16),
    paddingVertical: scale(6),
  },
  buttonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: scale(48),
    marginTop: scale(36),
  },
  loadingText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    textAlign: 'center',
    paddingVertical: scale(20),
  },
  errorText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.red,
    textAlign: 'center',
    paddingVertical: scale(20),
  },
  emptyText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.gray1,
    textAlign: 'center',
    paddingVertical: scale(20),
  },
});

export default memo(ManageBanks);
