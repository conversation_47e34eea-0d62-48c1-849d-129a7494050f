import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import React, {useState, useMemo, useCallback, memo} from 'react';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Button, Separator, SuccessScreen, TextInputCustom} from '../shared';
import {useBottomSheet} from 'context/BottomSheetModalContext';

const PasswordSettings = () => {
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [otp, setOtp] = useState('');
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleGetCode = useCallback(() => {}, []);

  const handleUpdate = useCallback(() => {
    openBottomSheet({
      children: (
        <SuccessScreen title="Password updated" onClose={closeBottomSheet} />
      ),
      showCloseBtn: true,
      enablePanDownToClose: true,
    });
  }, [openBottomSheet, closeBottomSheet]);

  const renderPasswordInput = useMemo(
    () => (
      <>
        <View style={styles.inputContainer}>
          <TextInputCustom
            label="Old Traders Room Password*"
            value={oldPassword}
            onChangeText={setOldPassword}
            secureTextEntry={!showOldPassword}
            rightIcon={
              <TouchableOpacity
                onPress={() => setShowOldPassword(!showOldPassword)}
                style={styles.eyeIcon}>
                <Image
                  source={images.ic_eye_off}
                  style={[
                    styles.icon,
                    showOldPassword && {tintColor: colors.yellow},
                  ]}
                />
              </TouchableOpacity>
            }
          />
          <Separator />
        </View>

        <View style={styles.inputContainer}>
          <TextInputCustom
            label="New Traderroom Password*"
            value={newPassword}
            onChangeText={setNewPassword}
            secureTextEntry={!showNewPassword}
            rightIcon={
              <TouchableOpacity
                onPress={() => setShowNewPassword(!showNewPassword)}
                style={styles.eyeIcon}>
                <Image
                  source={images.ic_eye_off}
                  style={[
                    styles.icon,
                    showNewPassword && {tintColor: colors.yellow},
                  ]}
                />
              </TouchableOpacity>
            }
          />
          <Separator />
        </View>

        <View style={styles.inputContainer}>
          <TextInputCustom
            label="Confirm New Traderroom Password*"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry={!showConfirmPassword}
            rightIcon={
              <TouchableOpacity
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                style={styles.eyeIcon}>
                <Image
                  source={images.ic_eye_off}
                  style={[
                    styles.icon,
                    showConfirmPassword && {tintColor: colors.yellow},
                  ]}
                />
              </TouchableOpacity>
            }
          />
          <Separator />
        </View>
      </>
    ),
    [
      oldPassword,
      newPassword,
      confirmPassword,
      showOldPassword,
      showNewPassword,
      showConfirmPassword,
    ],
  );

  const renderOtpSection = useMemo(
    () => (
      <View style={styles.inputSection}>
        <View style={styles.inputContent}>
          <Text style={styles.label}>OTP</Text>
          <View style={styles.inputWithButton}>
            <TextInput
              value={otp}
              onChangeText={setOtp}
              style={[styles.input, styles.otpInput]}
              placeholderTextColor={colors.gray_border}
              keyboardType="numeric"
            />
            <TouchableOpacity
              style={[styles.verifyButton]}
              onPress={handleGetCode}>
              <Text style={styles.verifyText}>Get Code</Text>
            </TouchableOpacity>
          </View>
        </View>
        <Separator />
      </View>
    ),
    [otp, handleGetCode],
  );

  const renderUpdateButton = useMemo(
    () => (
      <View style={styles.updateButtonContainer}>
        <Button title="Update" onPress={handleUpdate} />
      </View>
    ),
    [handleUpdate],
  );

  return (
    <View style={styles.container}>
      <Separator />
      {renderPasswordInput}
      {renderOtpSection}
      {renderUpdateButton}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  inputContainer: {
    paddingTop: scale(10),
  },
  inputSection: {
    height: scale(70),
    paddingTop: scale(10),
  },
  inputContent: {
    flex: 1,
    paddingHorizontal: scale(16),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  input: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    padding: 0,
    marginTop: scale(8),
    height: scale(30),
  },
  inputWithButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  otpInput: {
    flex: 1,
    marginRight: scale(20),
  },
  verifyButton: {
    height: scale(30),
    borderRadius: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scale(10),
    backgroundColor: colors.yellow,
  },
  verifyText: {
    color: colors.black,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  updateButtonContainer: {
    marginTop: scale(40),
    alignItems: 'center',
  },
  eyeIcon: {
    padding: scale(10),
  },
  icon: {
    width: scale(20),
    height: scale(20),
    tintColor: colors.white,
  },
});

export default memo(PasswordSettings);
