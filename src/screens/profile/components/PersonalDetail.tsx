import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import React, {useState, useMemo, useCallback, memo, useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {DropdownSection, Separator, Button} from '../shared';
import {profileActions} from 'store';
import {selectProfileInfo} from 'store/reducers/profile/selector';

interface InputFieldProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  isRequired?: boolean;
  renderSeparator: React.ReactNode;
}

const InputField = memo(({label, value, onChangeText, isRequired, renderSeparator}: InputFieldProps) => (
  <View style={styles.inputSection}>
    <View style={styles.inputContent}>
        <Text style={styles.label}>
          {label}
          {isRequired && '*'}
        </Text>
        {value && <Text style={styles.value}>{value}</Text>}
        {!value && (
          <TextInput
            value={value}
            onChangeText={onChangeText}
            style={styles.input}
            placeholderTextColor={colors.gray_border}
          />
        )}
      </View>
      {renderSeparator}
    </View>
  ),
);

const VerifyButton = memo(({isVerified}: {isVerified?: boolean}) => (
  <TouchableOpacity
    activeOpacity={0.8}
    disabled={isVerified}
    style={[styles.verifyButton, isVerified && styles.verifiedButton]}>
    <Text style={isVerified ? styles.verifiedText : styles.verifyText}>
      {isVerified ? 'Verified' : 'Verify now'}
    </Text>
  </TouchableOpacity>
));

const ContinueButton = memo(({onPress}: {onPress: () => void}) => (
  <View style={styles.buttonContainer}>
    <Button title="Continue" onPress={onPress} />
  </View>
));

const PersonalDetail = () => {
  const dispatch = useDispatch();
  const profileInfo = useSelector(selectProfileInfo);

  const [formData, setFormData] = useState({
    language: 'English',
    fullName: '',
    dateOfBirth: '',
    phone: '',
    email: '',
    address: '',
    district: '',
    city: '',
    stateProvince: '',
    postalCode: '',
    country: '',
  });

  useEffect(() => {
    dispatch(profileActions.getProfile());
  }, [dispatch]);

  useEffect(() => {
    if (profileInfo.data) {
      setFormData(prev => ({
        ...prev,
        fullName: profileInfo.data.name || '',
        dateOfBirth: profileInfo.data.birthday || '',
        phone: profileInfo.data.phone || '',
        email: profileInfo.data.email || '',
        address: profileInfo.data.address || '',
        district: profileInfo.data.district || '',
        city: profileInfo.data.city || '',
        stateProvince: profileInfo.data.state || '',
        postalCode: profileInfo.data.pcode || '',
        country: profileInfo.data.country || '',
      }));
    }
  }, [profileInfo.data]);

  const staticData = useMemo(
    () => ({
      languages: [
        {id: '1', name: 'English'},
        {id: '2', name: 'Vietnamese'},
      ],
      fullnames: [
        {id: '1', name: profileInfo.data?.name || ''},
      ],
      countries: [
        {id: '1', name: profileInfo.data?.country || ''},
        {id: '2', name: 'China'},
      ],
    }),
    [profileInfo.data],
  );

  const handleFormChange = useCallback(
    (field: string) => (value: string) => {
      setFormData(prev => ({...prev, [field]: value}));
    },
    [],
  );

  const handleContinue = useCallback(() => {}, []);

  const phoneSection = useMemo(
    () => (
      <View style={styles.inputSection}>
        <View style={styles.inputContent}>
          <View style={styles.phoneContainer}>
            <Text style={styles.label}>Phone Code</Text>
            <Text style={styles.label}>Phone*</Text>
          </View>
          <View style={styles.inputWithButton}>
            <TextInput
              value={formData.phone}
              onChangeText={handleFormChange('phone')}
              style={[styles.input, styles.phoneInput]}
              placeholderTextColor={colors.gray_border}
            />
            <VerifyButton isVerified={profileInfo.data?.has_verified_phone} />
          </View>
        </View>
        {<Separator />}
      </View>
    ),
    [formData.phone, handleFormChange, profileInfo.data?.has_verified_phone],
  );

  const emailSection = useMemo(
    () => (
      <View style={styles.inputSection}>
        <View style={styles.inputContent}>
          <Text style={styles.label}>Email*</Text>
          <View style={styles.inputWithButton}>
            <TextInput
              value={formData.email}
              onChangeText={handleFormChange('email')}
              style={[styles.input, styles.emailInput]}
              placeholderTextColor={colors.gray_border}
            />
            <VerifyButton isVerified={profileInfo.data?.has_verified_email} />
          </View>
        </View>
        {<Separator />}
      </View>
    ),
    [formData.email, handleFormChange, profileInfo.data?.has_verified_email],
  );

  if (profileInfo.loading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color={colors.yellow} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <DropdownSection
        label="Language"
        value={formData.language}
        data={staticData.languages}
        onSelect={handleFormChange('language')}
        zIndex={4000}
        renderSeparator={<Separator />}
      />
      <DropdownSection
        label="Fullname*"
        value={formData.fullName}
        data={staticData.fullnames}
        onSelect={handleFormChange('fullName')}
        zIndex={3000}
        renderSeparator={<Separator />}
      />
      <DropdownSection
        label="Date of Birth*"
        value={formData.dateOfBirth}
        data={[]}
        onSelect={handleFormChange('dateOfBirth')}
        zIndex={2000}
        renderSeparator={<Separator />}
      />
      {phoneSection}
      {emailSection}
      <InputField
        label="Address"
        value={formData.address}
        onChangeText={handleFormChange('address')}
        isRequired
        renderSeparator={<Separator />}
      />
      <InputField
        label="District"
        value={formData.district}
        onChangeText={handleFormChange('district')}
        isRequired
        renderSeparator={<Separator />}
      />
      <InputField
        label="City"
        value={formData.city}
        onChangeText={handleFormChange('city')}
        isRequired
        renderSeparator={<Separator />}
      />
      <InputField
        label="State/Province"
        value={formData.stateProvince}
        onChangeText={handleFormChange('stateProvince')}
        isRequired
        renderSeparator={<Separator />}
      />
      <InputField
        label="Postal Code"
        value={formData.postalCode}
        onChangeText={handleFormChange('postalCode')}
        renderSeparator={<Separator />}
      />
      <DropdownSection
        label="Country"
        value={formData.country}
        data={staticData.countries}
        onSelect={handleFormChange('country')}
        zIndex={1000}
        renderSeparator={<Separator />}
      />
      <ContinueButton onPress={handleContinue} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputSection: {
    height: scale(70),
    paddingTop: scale(10),
  },
  inputContent: {
    flex: 1,
    paddingHorizontal: scale(16),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  value: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.yellow,
  },
  input: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    padding: 0,
    marginTop: scale(8),
    height: scale(30),
  },
  inputWithButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  phoneInput: {
    flex: 1,
    marginRight: scale(20),
  },
  emailInput: {
    flex: 1,
    marginRight: scale(20),
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(20),
  },
  verifyButton: {
    height: scale(30),
    borderRadius: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scale(10),
    borderWidth: 1,
    borderColor: colors.gray_border,
  },
  verifyText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  verifiedButton: {
    backgroundColor: colors.yellow,
    borderWidth: 0,
    opacity: 1,
  },
  verifiedText: {
    color: colors.black,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
  },
  buttonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: scale(48),
    marginTop: scale(36),
  },
});

export default memo(PersonalDetail);
