import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import React, {useState, useMemo, useCallback, memo, useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {DropdownSection, Separator, Button, SuccessScreen} from '../../shared';
import {useBottomSheet} from 'context/BottomSheetModalContext';
import {useGlobalContext} from 'context/GlobalContext';
import {profileActions} from 'store';
import {
  selectWallets,
  selectWalletSystems,
  selectWalletNetworks,
  selectWalletCryptos,
} from 'store/reducers/profile/selector';
import WalletDetails from './WalletDetails';

interface WalletData {
  id?: number;
  name: string;
  system: string;
  network: string;
  crypto: string;
  address?: string;
  status?: string;
  created_at?: string;
}

interface WalletItemProps {
  name: string;
  system: string;
  network: string;
  onPress: () => void;
}

const WalletItem = memo(({name, system, network, onPress}: WalletItemProps) => (
  <TouchableOpacity
    activeOpacity={0.8}
    style={styles.walletRow}
    onPress={onPress}>
    <Text style={styles.walletText}>{name}</Text>
    <Text style={styles.walletText}>{system}</Text>
    <Text style={styles.walletText}>{network}</Text>
  </TouchableOpacity>
));

const ManageWallets = () => {
  const dispatch = useDispatch();
  const {openBottomSheet, closeBottomSheet} = useBottomSheet();
  const {handleLoading} = useGlobalContext();
  const walletsData = useSelector(selectWallets);
  const walletSystemsData = useSelector(selectWalletSystems);
  const walletNetworksData = useSelector(selectWalletNetworks);
  const walletCryptosData = useSelector(selectWalletCryptos);
  const [selectedSystem, setSelectedSystem] = useState('All');
  const [selectedNetwork, setSelectedNetwork] = useState('All');
  const [selectedCrypto, setSelectedCrypto] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);

  const systems = useMemo(() => {
    const allOption = [{id: 'All', name: 'All'}];
    if (walletSystemsData.data) {
      return [
        ...allOption,
        ...walletSystemsData.data.map((system: any) => ({
          id: system.code,
          name: system.name,
        })),
      ];
    }
    return allOption;
  }, [walletSystemsData.data]);

  const networks = useMemo(() => {
    const allOption = [{id: 'All', name: 'All'}];
    if (walletNetworksData.data) {
      return [
        ...allOption,
        ...walletNetworksData.data.map((network: any) => ({
          id: network.code,
          name: network.name,
        })),
      ];
    }
    return allOption;
  }, [walletNetworksData.data]);

  const cryptos = useMemo(() => {
    const allOption = [{id: 'All', name: 'All'}];
    if (walletCryptosData.data) {
      return [
        ...allOption,
        ...walletCryptosData.data.map((crypto: any) => ({
          id: crypto.code,
          name: crypto.name,
        })),
      ];
    }
    return allOption;
  }, [walletCryptosData.data]);

  const handleSystemSelect = useCallback((value: string) => {
    setSelectedSystem(value);
  }, []);

  const handleNetworkSelect = useCallback((value: string) => {
    setSelectedNetwork(value);
  }, []);

  const handleCryptoSelect = useCallback((value: string) => {
    setSelectedCrypto(value);
  }, []);

  // Load filter data on component mount
  useEffect(() => {
    dispatch(profileActions.getWalletSystems());
    dispatch(profileActions.getWalletNetworks());
    dispatch(profileActions.getWalletCryptos());
  }, [dispatch]);

  // Load wallets when filters change
  useEffect(() => {
    const params: any = {};
    if (selectedNetwork !== 'All') {
      params.network = selectedNetwork.toLowerCase();
    }
    if (selectedCrypto !== 'All') {
      params.crypto = selectedCrypto.toLowerCase();
    }
    if (selectedSystem !== 'All') {
      params.system = selectedSystem.toLowerCase();
    }
    params.page = currentPage;
    params.limit = 10;

    dispatch(profileActions.getWallets(params));
  }, [dispatch, selectedSystem, selectedNetwork, selectedCrypto, currentPage]);

  const handleSuccess = useCallback(
    ({isCreate}: {isCreate: boolean}) => {
      handleLoading(false);
      // Refresh wallet data
      const params: any = {};
      if (selectedNetwork !== 'All') {
        params.network = selectedNetwork.toLowerCase();
      }
      if (selectedCrypto !== 'All') {
        params.crypto = selectedCrypto.toLowerCase();
      }
      if (selectedSystem !== 'All') {
        params.system = selectedSystem.toLowerCase();
      }
      params.page = 1;
      params.limit = 10;
      dispatch(profileActions.getWallets(params));
      setCurrentPage(1);

      openBottomSheet({
        children: (
          <SuccessScreen
            onClose={() => {
              closeBottomSheet();
            }}
            title={isCreate ? 'Wallet Created' : 'Wallet Updated'}
          />
        ),
        showCloseBtn: true,
        enablePanDownToClose: true,
      });
    },
    [
      openBottomSheet,
      closeBottomSheet,
      handleLoading,
      dispatch,
      selectedSystem,
      selectedNetwork,
      selectedCrypto,
    ],
  );

  const handleCreateWallet = useCallback(() => {
    openBottomSheet({
      children: (
        <WalletDetails
          onSubmit={() => {
            handleLoading(true);
            setTimeout(() => {
              handleSuccess({isCreate: true});
            }, 1000);
          }}
        />
      ),
      showBackBtn: true,
      enablePanDownToClose: true,
    });
  }, [openBottomSheet, handleSuccess, handleLoading]);

  const handleWalletPress = useCallback(
    ({name, system, network, crypto, address}: WalletData) => {
      openBottomSheet({
        children: (
          <WalletDetails
            wallet={{name, system, network, crypto, address}}
            onSubmit={() => {
              handleLoading(true);
              setTimeout(() => {
                handleSuccess({isCreate: false});
              }, 1000);
            }}
          />
        ),
        showBackBtn: true,
        enablePanDownToClose: true,
      });
    },
    [openBottomSheet, handleSuccess, handleLoading],
  );

  const renderFilterSection = useMemo(
    () => (
      <>
        <DropdownSection
          label="System"
          value={selectedSystem}
          data={systems}
          onSelect={handleSystemSelect}
          zIndex={3000}
          renderSeparator={<Separator />}
        />
        <DropdownSection
          label="Network"
          value={selectedNetwork}
          data={networks}
          onSelect={handleNetworkSelect}
          zIndex={2000}
          renderSeparator={<Separator />}
        />
        <DropdownSection
          label="Crypto"
          value={selectedCrypto}
          data={cryptos}
          onSelect={handleCryptoSelect}
          zIndex={1000}
          renderSeparator={<Separator />}
        />
      </>
    ),
    [
      selectedSystem,
      selectedNetwork,
      selectedCrypto,
      systems,
      networks,
      cryptos,
      handleSystemSelect,
      handleNetworkSelect,
      handleCryptoSelect,
    ],
  );

  const renderWalletTitle = useMemo(
    () => (
      <View style={styles.titleContainer}>
        <Text style={styles.title}>My Wallet</Text>
      </View>
    ),
    [],
  );

  const renderTableHeader = useMemo(
    () => (
      <>
        <Separator />
        <View style={styles.tableHeader}>
          <Text style={styles.headerText}>Name</Text>
          <Text style={styles.headerText}>System</Text>
          <Text style={styles.headerText}>Network</Text>
        </View>
        <Separator />
      </>
    ),
    [],
  );

  const renderWallets = useMemo(() => {
    if (walletsData.loading) {
      return (
        <View style={styles.walletsContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      );
    }

    if (walletsData.error) {
      return (
        <View style={styles.walletsContainer}>
          <Text style={styles.errorText}>Error loading wallets</Text>
        </View>
      );
    }

    if (!walletsData.data || walletsData.data.length === 0) {
      return (
        <View style={styles.walletsContainer}>
          <Text style={styles.emptyText}>No wallets found</Text>
        </View>
      );
    }

    return (
      <View style={styles.walletsContainer}>
        {walletsData.data.map((wallet: any) => (
          <WalletItem
            key={wallet.id}
            name={wallet.name}
            system={wallet.system}
            network={wallet.network}
            onPress={() =>
              handleWalletPress({
                name: wallet.name,
                system: wallet.system,
                network: wallet.network,
                crypto: wallet.crypto,
                address: wallet.address,
              })
            }
          />
        ))}
      </View>
    );
  }, [walletsData, handleWalletPress]);

  const handleLoadMore = useCallback(() => {
    if (
      walletsData.pagination &&
      currentPage < walletsData.pagination.last_page
    ) {
      setCurrentPage(prev => prev + 1);
    }
  }, [walletsData.pagination, currentPage]);

  const renderLoadMore = useMemo(() => {
    if (
      !walletsData.pagination ||
      currentPage >= walletsData.pagination.last_page
    ) {
      return null;
    }

    return (
      <Button
        title="Load More"
        variant="secondary"
        style={styles.loadMoreButton}
        onPress={handleLoadMore}
      />
    );
  }, [walletsData.pagination, currentPage, handleLoadMore]);

  const renderCreateButton = useMemo(
    () => (
      <View style={styles.buttonContainer}>
        <Button title="Create New Wallet" onPress={handleCreateWallet} />
      </View>
    ),
    [handleCreateWallet],
  );

  return (
    <View style={styles.container}>
      {renderFilterSection}
      {renderWalletTitle}
      {renderTableHeader}
      {renderWallets}
      {renderLoadMore}
      {renderCreateButton}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  titleContainer: {
    marginTop: scale(24),
    marginBottom: scale(16),
    paddingHorizontal: scale(16),
  },
  title: {
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  tableHeader: {
    flexDirection: 'row',
    paddingVertical: scale(10),
    backgroundColor: colors.black2,
    paddingHorizontal: scale(16),
  },
  headerText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    width: '33%',
  },
  walletsContainer: {},
  walletRow: {
    flexDirection: 'row',
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '50',
  },
  walletText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    width: '33%',
  },
  loadMoreButton: {
    marginVertical: scale(24),
    paddingHorizontal: scale(16),
    paddingVertical: scale(6),
  },
  buttonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: scale(48),
    marginTop: scale(36),
  },
  loadingText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    textAlign: 'center',
    paddingVertical: scale(20),
  },
  errorText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.red,
    textAlign: 'center',
    paddingVertical: scale(20),
  },
  emptyText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.gray1,
    textAlign: 'center',
    paddingVertical: scale(20),
  },
});

export default memo(ManageWallets);
