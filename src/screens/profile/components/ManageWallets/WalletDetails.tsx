import {View, Text, StyleSheet, TextInput} from 'react-native';
import React, {useState, useMemo, memo} from 'react';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {DropdownSection, Separator, Button} from '../../shared';

interface WalletData {
  name: string;
  system: string;
  network: string;
  crypto: string;
  address?: string;
}

interface WalletDetailsProps {
  wallet?: WalletData;
  onSubmit?: () => void;
  systems?: Array<{id: string; name: string}>;
  networks?: Array<{id: string; name: string}>;
  cryptos?: Array<{id: string; name: string}>;
}

const WalletDetails = ({
  wallet,
  onSubmit,
  systems = [],
  networks = [],
  cryptos = [],
}: WalletDetailsProps) => {
  const [walletName, setWalletName] = useState(wallet?.name || '');
  const [system, setSystem] = useState(wallet?.system || 'All');
  const [network, setNetwork] = useState(wallet?.network || 'All');
  const [crypto, setCrypto] = useState(wallet?.crypto || 'All');

  const isCreate = !wallet;

  const renderHeader = useMemo(
    () => (
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>
          {isCreate ? 'Create New Wallet' : 'Update'}
        </Text>
      </View>
    ),
    [isCreate],
  );

  const renderForm = useMemo(
    () => (
      <View style={styles.formContainer}>
        <View style={styles.inputSection}>
          <View style={styles.inputContent}>
            <Text style={styles.label}>Wallet Name*</Text>
            <TextInput
              style={styles.input}
              value={walletName}
              onChangeText={setWalletName}
              placeholder="Enter wallet name"
              placeholderTextColor={colors.gray_border}
            />
          </View>
          <Separator />
        </View>
        <DropdownSection
          label="System*"
          value={system}
          data={systems}
          onSelect={(item: any) => setSystem(item.id)}
          zIndex={3000}
          isYellow={isCreate ? true : false}
          renderSeparator={<Separator />}
        />
        <DropdownSection
          label="Network*"
          value={network}
          data={networks}
          onSelect={(item: any) => setNetwork(item.id)}
          zIndex={2000}
          isYellow={isCreate ? true : false}
          renderSeparator={<Separator />}
        />
        <DropdownSection
          label="Crypto*"
          value={crypto}
          data={cryptos}
          onSelect={(item: any) => setCrypto(item.id)}
          zIndex={1000}
          isYellow={isCreate ? true : false}
          renderSeparator={<Separator />}
        />
        {isCreate ? (
          <View style={styles.addressContainer}>
            <Text style={[styles.addressText, styles.placeholderText]}>
              Address*
            </Text>
          </View>
        ) : (
          <View style={styles.addressContainer}>
            <Text style={styles.addressText}>{wallet?.address}</Text>
          </View>
        )}
      </View>
    ),
    [
      isCreate,
      walletName,
      system,
      network,
      crypto,
      systems,
      networks,
      cryptos,
      wallet?.address,
    ],
  );

  const renderActionButton = useMemo(
    () => (
      <View style={styles.buttonContainer}>
        <Button title={isCreate ? 'Create' : 'Update'} onPress={onSubmit} />
      </View>
    ),
    [isCreate, onSubmit],
  );

  return (
    <View style={styles.container}>
      {renderHeader}
      {renderForm}
      {renderActionButton}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: scale(100),
  },
  headerContainer: {
    paddingBottom: scale(24),
  },
  headerTitle: {
    fontSize: scale(20),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginBottom: scale(55),
    paddingHorizontal: scale(20),
  },
  formContainer: {
    flex: 1,
  },
  inputSection: {
    height: scale(70),
    paddingTop: scale(10),
  },
  inputContent: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: scale(16),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  input: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    paddingVertical: scale(8),
  },
  addressContainer: {
    paddingHorizontal: scale(16),
    paddingVertical: scale(12),
    borderBottomWidth: 1,
    borderColor: colors.gray_border + '20',
    height: scale(70),
    justifyContent: 'center',
  },
  addressText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
  placeholderText: {
    color: colors.gray_border,
  },
  buttonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: scale(48),
    marginTop: scale(36),
  },
});

export default memo(WalletDetails);
