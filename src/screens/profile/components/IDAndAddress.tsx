import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import React, {useState, useCallback, useMemo, memo} from 'react';
import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';
import {Separator} from '../shared';

interface OptionCheckboxProps {
  option: string;
  isSelected: boolean;
  onPress: (option: string) => void;
}

interface UploadBoxProps {
  title: string;
  onPress?: () => void;
}

const OptionCheckbox = memo(({option, isSelected, onPress}: OptionCheckboxProps) => (
  <TouchableOpacity
    style={styles.optionContainer}
    activeOpacity={0.8}
    onPress={() => onPress(option)}>
    <View style={[styles.checkbox, isSelected && styles.selectedCheckbox]}>
        <Image
          source={images.ic_check}
          style={[styles.checkmark, isSelected && styles.checkmarkSelected]}
        />
      </View>
      <Text
        style={[styles.optionText, isSelected && styles.selectedOptionText]}>
        {option}
      </Text>
    </TouchableOpacity>
  ),
);

const UploadBox = memo(({title, onPress}: UploadBoxProps) => (
  <TouchableOpacity
    style={styles.uploadContainer}
    activeOpacity={0.7}
    onPress={onPress}>
    <Image source={images.ic_upload_file} style={styles.iconPlaceholder} />
    <Text style={styles.uploadText}>{title}</Text>
  </TouchableOpacity>
));


const IDAndAddress = () => {
  const [selectedOption, setSelectedOption] = useState('ID Card');

  const handleOptionPress = useCallback((option: string) => {
    setSelectedOption(option);
  }, []);

  const handleUpload = useCallback(() => {}, []);

  const options = useMemo(() => ['ID Card', 'Passport', "Drive's License"], []);

  const identityInstructions = useMemo(
    () => 'Please choose one of the following options',
    [],
  );

  const addressInstructions = useMemo(
    () =>
      'We require a copy of a Bank / Credit Card Statement or Utility Bills ' +
      '(Electricity, Water, Gas, Council Tax, Tax Letter, Phone, Television ' +
      'Cable or Internet) in your name and issued within the last six months.',
    [],
  );

  const renderIdentitySection = useMemo(
    () => (
      <View style={styles.section}>
        <Separator />
        <View style={styles.sectionContent}>
          <Text style={styles.sectionTitle}>Identity Verification</Text>
          <Text style={styles.subtitle}>{identityInstructions}</Text>

          <View style={styles.optionsRow}>
            {options.map(option => (
              <OptionCheckbox
                key={option}
                option={option}
                isSelected={selectedOption === option}
                onPress={handleOptionPress}
              />
            ))}
          </View>

          <View style={styles.uploadBoxContainer}>
            <UploadBox title="Upload Front" onPress={handleUpload} />
          </View>
          <View style={styles.uploadBoxContainer}>
            <UploadBox title="Upload Back" onPress={handleUpload} />
          </View>
        </View>
        <Separator />
      </View>
    ),
    [
      options,
      selectedOption,
      handleOptionPress,
      handleUpload,
      identityInstructions,
    ],
  );

  const renderAddressSection = useMemo(
    () => (
      <View style={styles.section}>
        <View style={styles.sectionContent}>
          <Text style={styles.sectionTitle}>Address Verification</Text>
          <Text style={styles.subtitle}>{addressInstructions}</Text>

          <View style={styles.uploadBoxContainer}>
            <UploadBox title="Upload Front" onPress={handleUpload} />
          </View>
        </View>
      </View>
    ),
    [addressInstructions, handleUpload],
  );

  return (
    <View style={styles.container}>
      {renderIdentitySection}
      {renderAddressSection}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
  },
  sectionContent: {
    paddingVertical: scale(20),
    paddingHorizontal: scale(16),
  },
  sectionTitle: {
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginBottom: scale(8),
  },
  subtitle: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    marginBottom: scale(20),
  },
  optionsRow: {
    flexDirection: 'row',
    marginBottom: scale(24),
    marginTop: scale(10),
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: scale(25),
    paddingVertical: scale(5),
    paddingRight: scale(10),
  },
  checkbox: {
    width: scale(20),
    height: scale(20),
    borderWidth: 2,
    borderColor: colors.white,
    borderRadius: scale(2),
    marginRight: scale(5),
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.6,
  },
  selectedCheckbox: {
    borderColor: colors.yellow,
    opacity: 1,
  },
  checkmark: {
    width: scale(10),
    height: scale(10),
    tintColor: colors.white,
  },
  checkmarkSelected: {
    tintColor: colors.yellow,
  },
  optionText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  selectedOptionText: {
    color: colors.yellow,
  },
  uploadBoxContainer: {
    marginBottom: scale(15),
  },
  uploadContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: scale(70),
    borderWidth: 1.5,
    borderColor: colors.gray_border + '50',
    borderRadius: scale(5),
    gap: scale(10),
  },
  iconPlaceholder: {
    width: scale(30),
    height: scale(30),
  },
  uploadText: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
});

export default memo(IDAndAddress);
