import React, {memo, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import {colors, fonts} from 'assets';
import {scale} from 'utils/device';
import {Button} from '../shared';
import {selectGoogle2FA} from 'store/reducers/profile/selector';
import {profileActions} from 'store';

interface Google2FABottomSheetProps {
  onClose: () => void;
}

const Google2FABottomSheet: React.FC<Google2FABottomSheetProps> = ({
  onClose,
}) => {
  const dispatch = useDispatch();
  const google2FAState = useSelector(selectGoogle2FA);
  const {loading, data, error} = google2FAState;

  const handleContinue = useCallback(() => {
    // TODO: Navigate to verification step
    onClose();
  }, [onClose]);

  const handleRetry = useCallback(() => {
    dispatch(profileActions.getGoogle2FA());
  }, [dispatch]);

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>SET UP VIA GOOGLE AUTHENTICATOR</Text>
          <Text style={styles.subtitle}>
            Step 1: Scan the QR code with your Google Authenticator app
          </Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.yellow} />
          <Text style={styles.loadingText}>Generating QR Code...</Text>
        </View>
      </View>
    );
  }

  if (error || !data) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>SET UP VIA GOOGLE AUTHENTICATOR</Text>
          <Text style={styles.subtitle}>
            Failed to generate QR code. Please try again.
          </Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Unable to generate QR code. Please check your connection and try
            again.
          </Text>
          <Button
            title="Retry"
            onPress={handleRetry}
            style={styles.retryButton}
            textStyle={styles.retryButtonText}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>SET UP VIA GOOGLE AUTHENTICATOR</Text>
        <Text style={styles.subtitle}>
          Step 1: Scan the QR code with your Google Authenticator app
        </Text>
      </View>

      <View style={styles.qrContainer}>
        <Image
          source={{uri: data.qr_image}}
          style={styles.qrImage}
          resizeMode="contain"
        />
      </View>

      <View style={styles.secretContainer}>
        <Text style={styles.secretLabel}>
          Step 2: Enter the code from your app
        </Text>
        <View style={styles.secretBox}>
          <Text style={styles.secretText}>{data.secret}</Text>
        </View>
        <Text style={styles.secretNote}>
          If you can't scan the QR code, enter this code manually
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title="Continue"
          onPress={handleContinue}
          style={styles.continueButton}
          textStyle={styles.continueButtonText}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: scale(20),
    paddingVertical: scale(20),
  },
  header: {
    alignItems: 'center',
    marginBottom: scale(30),
  },
  title: {
    fontSize: scale(18),
    fontFamily: fonts.SFPro.semiBold,
    color: colors.white,
    textAlign: 'center',
    marginBottom: scale(10),
  },
  subtitle: {
    fontSize: scale(14),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    textAlign: 'center',
    opacity: 0.8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: scale(16),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginTop: scale(20),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scale(20),
  },
  errorText: {
    fontSize: scale(16),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    textAlign: 'center',
    marginBottom: scale(30),
    opacity: 0.8,
  },
  retryButton: {
    backgroundColor: colors.yellow,
    paddingHorizontal: scale(40),
  },
  retryButtonText: {
    color: colors.black,
  },
  qrContainer: {
    alignItems: 'center',
    marginBottom: scale(30),
    backgroundColor: colors.white,
    borderRadius: scale(15),
    padding: scale(20),
  },
  qrImage: {
    width: scale(200),
    height: scale(200),
  },
  secretContainer: {
    marginBottom: scale(30),
  },
  secretLabel: {
    fontSize: scale(16),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    textAlign: 'center',
    marginBottom: scale(15),
  },
  secretBox: {
    backgroundColor: colors.gray_border,
    borderRadius: scale(8),
    padding: scale(15),
    marginBottom: scale(10),
  },
  secretText: {
    fontSize: scale(18),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    textAlign: 'center',
    letterSpacing: scale(2),
  },
  secretNote: {
    fontSize: scale(12),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
    textAlign: 'center',
    opacity: 0.6,
  },
  buttonContainer: {
    marginTop: 'auto',
  },
  continueButton: {
    backgroundColor: colors.yellow,
  },
  continueButtonText: {
    color: colors.black,
  },
});

export default memo(Google2FABottomSheet);
