import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Image,
} from 'react-native';
import React, {useEffect, useMemo, useCallback} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useIsFocused} from '@react-navigation/native';
import {profileActions, screenFocusActions} from 'store';
import {Header, WrapperContainer} from 'components';
import {fonts, images} from 'assets';
import {colors} from 'assets';
import {scale} from 'utils/device';
import {
  PersonalDetail,
  IDAndAddress,
  ManageWallets,
  ManageBanks,
  PasswordSettings,
  Security,
  LegalDocuments,
} from './components';
import {selectProfileInfo} from 'store/reducers/profile/selector';

interface Props {}

interface TabItemProps {
  label: string;
  isActive: boolean;
  onPress: () => void;
}

interface VerifiedHeaderProps {
  isVerified: boolean;
}

const TabItem: React.FC<TabItemProps> = React.memo(
  ({label, isActive, onPress}) => (
    <TouchableOpacity
      activeOpacity={0.8}
      style={styles.tabItem}
      onPress={onPress}>
      <Text style={styles.tabText}>{label}</Text>
      {isActive && <View style={styles.activeDot} />}
    </TouchableOpacity>
  ),
);

const VerifiedHeader: React.FC<VerifiedHeaderProps> = React.memo(({isVerified}) => (
  <View style={styles.headerContainer}>
    <Text style={styles.title}>Profile</Text>
    {isVerified && (
      <View style={styles.verifiedContainer}>
        <Text style={styles.subtitle}> Successfully Verified</Text>
        <Image source={images.ic_verified} style={styles.verifiedIcon} />
      </View>
    )}
  </View>
));

const ProfileScreen: React.FC<Props> = () => {
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const [activeTab, setActiveTab] = React.useState(0);
  const profileInfo = useSelector(selectProfileInfo);

  useEffect(() => {
    if (isFocused) {
      dispatch(screenFocusActions.setCurrentScreen('ProfileScreen'));
      dispatch(profileActions.getProfile());
    }
  }, [isFocused, dispatch]);

  const profileItems = useMemo(
    () => [
      'Personal Detail',
      'ID & Address',
      'Manage Wallets',
      'Manage Banks',
      'Password Settings',
      'Security',
      'Legal Documents',
    ],
    [],
  );

  const handleTabPress = useCallback(
    (index: number) => () => setActiveTab(index),
    [],
  );

  const renderTabHeader = useMemo(() => {
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tabContainer}>
        {profileItems.map((item, index) => (
          <TabItem
            key={item}
            label={item}
            isActive={index === activeTab}
            onPress={handleTabPress(index)}
          />
        ))}
      </ScrollView>
    );
  }, [activeTab, profileItems, handleTabPress]);

  const renderContent = useMemo(() => {
    switch (activeTab) {
      case 0:
        return <PersonalDetail />;
      case 1:
        return <IDAndAddress />;
      case 2:
        return <ManageWallets />;
      case 3:
        return <ManageBanks />;
      case 4:
        return <PasswordSettings />;
      case 5:
        return <Security />;
      case 6:
        return <LegalDocuments />;
      default:
        return null;
    }
  }, [activeTab]);

  const isVerified = useMemo(() => {
    return profileInfo.data?.is_verify === 1;
  }, [profileInfo.data]);

  return (
    <WrapperContainer style={{backgroundColor: colors.black2}}>
      <Header />
      <View style={styles.container}>
        <VerifiedHeader isVerified={isVerified} />
        {renderTabHeader}
      </View>
      <ScrollView
        style={styles.contentContainer}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>{renderContent}</View>
      </ScrollView>
    </WrapperContainer>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: colors.background,
  },
  container: {
    paddingHorizontal: scale(16),
    paddingTop: scale(34),
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: scale(40),
  },
  title: {
    fontSize: scale(30),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
  },
  verifiedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(2),
  },
  subtitle: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.yellow,
  },
  verifiedIcon: {
    width: scale(24),
    height: scale(24),
  },
  tabContainer: {
    gap: scale(16),
    paddingBottom: scale(20),
  },
  tabItem: {
    position: 'relative',
    alignItems: 'center',
  },
  activeDot: {
    width: scale(5),
    height: scale(5),
    backgroundColor: colors.yellow,
    borderRadius: scale(5),
    position: 'absolute',
    bottom: scale(-8),
  },
  tabText: {
    color: colors.white,
    fontSize: scale(17),
    fontFamily: fonts.SFPro.medium,
  },
  contentContainer: {},
  content: {
    gap: scale(24),
  },
  inputContainer: {
    gap: scale(8),
  },
  inputSection: {
    paddingTop: scale(16),
  },
  label: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.medium,
    color: colors.white,
    marginBottom: scale(16),
  },
  input: {
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
    color: colors.white,
  },
});

export default React.memo(ProfileScreen);
