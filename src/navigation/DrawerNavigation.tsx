import {
  createDrawerNavigator,
  DrawerContentScrollView,
  DrawerItem,
} from '@react-navigation/drawer';
import {colors, fonts, images} from 'assets';
import React, {useState} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {scale} from 'utils/device';
import BottomTabs from './BottomTab';
import {useSelector} from 'react-redux';
import {AccountScreen} from 'screens';
const Drawer = createDrawerNavigator();

function CustomDrawerContent(props) {
  const {currentScreen} = useSelector((state: any) => state.screenFocus);
  const [index, setIndex] = useState(0);
  const drawerScreenList = [
    {
      id: 1,
      label: 'Dashboard',
      icon: images.ic_contact_active,
      stack: 'BottomTabs',
      screenName: 'HomeScreen',
    },
    {
      id: 2,
      label: 'Transaction',
      icon: images.ic_transaction_inactive,
      stack: 'BottomTabs',
      screenName: 'TransactionScreen',
    },
    {
      id: 3,
      label: 'Refer',
      icon: images.ic_refer_active,
      stack: 'BottomTabs',
      screenName: 'ReferScreen',
    },
    {
      id: 4,
      label: 'Tools',
      icon: images.ic_tools_inactive,
      stack: 'BottomTabs',
      screenStack: 'ToolsStack',
      screenName: 'ToolsScreen',
    },
    {
      id: 5,
      label: 'Promotion',
      icon: images.ic_promotion_active,
      stack: 'BottomTabs',
      screenStack: 'ToolsStack',
      screenName: 'PromotionScreen',
    },
    {
      id: 6,
      label: 'Announcement',
      icon: images.ic_announcement_active,
      stack: 'BottomTabs',
      screenStack: 'ToolsStack',
      screenName: 'AnnouncementScreen',
    },
    {
      id: 7,
      label: 'Profile',
      icon: images.ic_profile_inactive,
      stack: 'BottomTabs',
      screenName: 'ProfileScreen',
    },
    {
      id: 8,
      label: 'Contact',
      icon: images.ic_contact_active,
      stack: 'BottomTabs',
      screenStack: 'ToolsStack',
      screenName: 'ContactScreen',
    },
  ];

  const onPressCloseButton = () => {
    props.navigation.closeDrawer();
  };

  return (
    <DrawerContentScrollView {...props}>
      <TouchableOpacity style={styles.closeButton} onPress={onPressCloseButton}>
        <Image source={images.ic_close} style={styles.iconClose} />
      </TouchableOpacity>
      {drawerScreenList.map(screen => (
        <DrawerItem
          style={{marginVertical: scale(5)}}
          key={screen.label}
          onPress={() => {
            if (screen.screenStack) {
              props.navigation.navigate(screen.stack, {
                screen: screen.screenStack,
                params: {
                  screen: screen.screenName,
                },
              });
            } else {
              props.navigation.navigate(screen.stack, {
                screen: screen.screenName,
              });
            }
          }}
          focused={screen.screenName === currentScreen}
          activeTintColor={colors.yellow}
          inactiveTintColor={colors.inactiveIcon}
          activeBackgroundColor={colors.black}
          label={({focused, color}) => (
            <Text style={styles.labelStyle}>{screen.label}</Text>
          )}
          icon={({focused, color, size}) => (
            <Image
              source={screen.icon}
              style={[styles.icon, {tintColor: color}]}
            />
          )}
        />
      ))}
    </DrawerContentScrollView>
  );
}

function DrawerNavigation() {
  return (
    <View style={{flex: 1}}>
      <Drawer.Navigator
        drawerContent={props => <CustomDrawerContent {...props} />}
        screenOptions={{
          headerShown: false,
          drawerContentContainerStyle: {
            justifyContent: 'center',
          },
          drawerPosition: 'right',
          drawerStyle: {
            backgroundColor: colors.black,
            width: scale(390),
          },
          drawerLabelStyle: styles.labelStyle,
          drawerActiveBackgroundColor: colors.black,
          drawerActiveTintColor: colors.yellow,
          drawerType: 'front',
          swipeEnabled: false,
        }}>
        <Drawer.Screen name="BottomTabs" component={BottomTabs} />
        <Drawer.Screen name="AccountScreen" component={AccountScreen} />
      </Drawer.Navigator>
    </View>
  );
}

export default DrawerNavigation;

const styles = StyleSheet.create({
  labelStyle: {
    color: colors.white,
    fontFamily: fonts.SFPro.semiBold,
    fontSize: 20,
  },
  icon: {
    width: scale(30),
    height: scale(30),
  },
  iconClose: {
    width: scale(16),
    height: scale(16),
  },
  closeButton: {
    padding: scale(10),
    width: scale(36),
    height: scale(36),
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: scale(16),
  },
});
