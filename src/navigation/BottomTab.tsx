import {
  createBottomTabNavigator,
  TransitionSpecs,
} from '@react-navigation/bottom-tabs';
import React, {useMemo} from 'react';
import {Image, Text} from 'react-native';

const Tab = createBottomTabNavigator();

import {colors, fonts, images} from 'assets';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import HomeScreen from 'screens/home/<USER>';
import ProfileScreen from 'screens/profile/ProfileScreen';
import TransactionScreen from 'screens/transaction/TransactionScreen';
import {scale, vScale} from 'utils/device';
import ToolsStack from './ToolsStack';
import ReferScreen from 'screens/refer/ReferScreen';

const TabBarIcon = ({color, icon}: {color: string; icon: any}) => (
  <Image
    source={icon}
    style={[
      {
        height: scale(30),
        width: scale(30),
        tintColor: color,
      },
    ]}
  />
);

const TabBarLabel = ({label}: {label: string}) => (
  <Text
    style={{
      fontFamily: fonts.SFPro.semiBold,
      fontSize: 15,
      color: colors.white,
      marginTop: vScale(9),
    }}>
    {label}
  </Text>
);

export default function BottomTabs() {
  const insets = useSafeAreaInsets();

  const visibleTabs = useMemo(
    () => [
      {
        name: 'HomeScreen',
        component: HomeScreen,
        label: 'Home',
        icon: images.ic_home_inactive,
      },
      {
        name: 'TransactionScreen',
        component: TransactionScreen,
        label: 'Transaction',
        icon: images.ic_transaction_inactive,
      },
      {
        name: 'ToolsStack',
        component: ToolsStack,
        label: 'Tools',
        icon: images.ic_tools_inactive,
      },
      {
        name: 'ProfileScreen',
        component: ProfileScreen,
        label: 'Mine',
        icon: images.ic_profile_inactive,
      },
    ],
    [],
  );

  const hiddenTabs = useMemo(
    () => [
      {
        name: 'ReferScreen',
        component: ReferScreen,
      },
    ],
    [],
  );

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#F58A32',
        tabBarInactiveTintColor: '#747376',
        tabBarStyle: {
          backgroundColor: '#0F0F14',
          height: scale(90 + insets.bottom),
          paddingTop: vScale(16),
          paddingBottom: insets.bottom,
        },
        transitionSpec: TransitionSpecs.ShiftSpec,
      }}>
      {visibleTabs.map(tab => (
        <Tab.Screen
          key={tab.name}
          name={tab.name}
          component={tab.component}
          options={{
            tabBarLabel: () => <TabBarLabel label={tab.label} />,
            tabBarIcon: ({color}: {color: string}) => (
              <TabBarIcon color={color} icon={tab.icon} />
            ),
          }}
        />
      ))}
      {hiddenTabs.map(tab => (
        <Tab.Screen
          key={tab.name}
          name={tab.name}
          component={tab.component}
          options={{
            tabBarItemStyle: {display: 'none'},
            tabBarStyle: {
              backgroundColor: '#0F0F14',
              height: scale(90 + insets.bottom),
              paddingTop: vScale(16),
              paddingBottom: insets.bottom,
            },
          }}
        />
      ))}
    </Tab.Navigator>
  );
}
