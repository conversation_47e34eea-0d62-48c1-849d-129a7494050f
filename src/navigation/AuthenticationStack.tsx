import {createNativeStackNavigator} from '@react-navigation/native-stack';
import * as React from 'react';
import {
  LoginScreen,
  SignupScreen,
  ForgetPasswordScreen,
  WelcomeScreen,
  SplashScreen,
} from 'screens';

const Stack = createNativeStackNavigator();

const AuthenticationStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
      }}>
      <Stack.Screen name="Splash" component={SplashScreen} />
      {/* The Splash screen is the first screen that appears when the app is launched */}
      {/* It is used to show a loading indicator or a logo while the app is initializing */}
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Signup" component={SignupScreen} />
      <Stack.Screen name="ForgetPassword" component={ForgetPasswordScreen} />
    </Stack.Navigator>
  );
};

export default AuthenticationStack;
