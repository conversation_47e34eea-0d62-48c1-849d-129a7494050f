import {createNavigationContainerRef, useRoute} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import * as React from 'react';
import AnnouncementScreen from 'screens/announcement/AnnouncementScreen';
import ContactScreen from 'screens/contact/ContactScreen';

import PromotionScreen from 'screens/promotion/PromotionScreen';
import ToolsScreen from 'screens/tools/ToolsScreen';

const Stack = createNativeStackNavigator();

export default function ToolsStack() {
  return (
    <Stack.Navigator
      initialRouteName="ToolsScreen"
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
      }}>
      <Stack.Screen name="ToolsScreen" component={ToolsScreen} />
      <Stack.Screen name="PromotionScreen" component={PromotionScreen} />
      <Stack.Screen name="AnnouncementScreen" component={AnnouncementScreen} />
      <Stack.Screen name="ContactScreen" component={ContactScreen} />
    </Stack.Navigator>
  );
}
