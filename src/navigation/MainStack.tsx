import {createNativeStackNavigator} from '@react-navigation/native-stack';
import * as React from 'react';

import {NewsDetailScreen} from 'screens';
import DrawerNavigation from './DrawerNavigation';

const Stack = createNativeStackNavigator();

export default function MainStack() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
      }}>
      <Stack.Screen name="DrawerNavigation" component={DrawerNavigation} />
      <Stack.Screen name="NewsDetailScreen" component={NewsDetailScreen} />
    </Stack.Navigator>
  );
}
