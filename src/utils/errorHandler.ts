export const formatError = (error: any): string => {
  console.log('formatError', error);

  if (!error) return 'Unknown error occurred';

  if (error.response) {
    return error.response.data?.message || 'Something went wrong';
  }

  if (error.message) {
    return error.message;
  }
  if (error?.data) {
    if (error?.data?.error_fields) {
      let errorMessage = Object.values(error?.data?.error_fields).flat();
      return { ...error?.data, message: errorMessage.join('\n') };
    } else
      return error.data
  }


  return 'An unexpected error occurred';
};
