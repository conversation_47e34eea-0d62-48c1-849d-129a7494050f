import {Dimensions, Platform, PixelRatio} from 'react-native';
import {hasNotch} from 'react-native-device-info';

const designWidth = 440;
const designHeight = 956;

const screen = Dimensions.get('screen');

let device = {
  w: Dimensions.get('window').width,
  h: Dimensions.get('window').height,
  width: designWidth,
  s: Dimensions.get('window').width / designWidth,
  l: Dimensions.get('window').height / designHeight,
};
const screenHeight = Dimensions.get('window').height;

const screenWidth = Dimensions.get('window').width;

export const {width, height} = Dimensions.get('window');
const guidelineBaseWidth = 440;

const scale = (size: number) =>
  PixelRatio.roundToNearestPixel((width / guidelineBaseWidth) * size);

const vScale = (size: number) => {
  return Math.round(size * device.l);
};

const isIos = () => {
  return Platform.OS == 'ios';
};

const isNavigationTab = () => {
  return isIos() && hasNotch();
};

const NAVIGATION_TAB = scale(34);

const NAVIGATION_TAB_ANDROID = screen.height - device.h;

const PADDING_HORIZONTAL = scale(16);

const HEADER_HEIGHT = scale(56);

export {
  device,
  scale,
  vScale,
  isIos,
  isNavigationTab,
  screenHeight,
  screenWidth,
  NAVIGATION_TAB,
  PADDING_HORIZONTAL,
  NAVIGATION_TAB_ANDROID,
  HEADER_HEIGHT,
};
