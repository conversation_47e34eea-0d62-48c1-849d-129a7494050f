import CodePush from '@revopush/react-native-code-push';
import {images} from 'assets';

export const capitalizeName = (str: string) => {
  if (!str) {
    return '';
  } else {
    return str
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize the first letter of each word
      .join(' ');
  }
};

export const renderTransactionIcon = item => {
  switch (item?.type) {
    case 1:
      return images.ic_deposit;
    case 2:
      return images.ic_withdraw;
    case 3:
      return images.ic_transfer;
  }
};

export const validateEmail = (email: string) => {
  const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return re.test(email);
};

export const convertDateCreated = (date: string) => {
  const dateObj = new Date(date);

  const day = String(dateObj.getDate()).padStart(2, '0'); // Get day and pad with 0 if needed
  const month = String(dateObj.getMonth() + 1).padStart(2, '0'); // Get month (0-based) and pad with 0
  const year = dateObj.getFullYear(); // Get full year
  const hours = String(dateObj.getHours()).padStart(2, '0'); // Get hours and pad with 0
  const minutes = String(dateObj.getMinutes()).padStart(2, '0'); // Get minutes and pad with 0

  return `${day}-${month}-${year} ${hours}:${minutes}`; // Construct the formatted string
};

export const convertDateYYMMDD = (
  date: string,
  showTime: boolean = false,
  is12HourFormat: boolean = false,
) => {
  const dateObj = new Date(date);

  const day =
    dateObj.getDate() < 10 ? `0${dateObj.getDate()}` : dateObj.getDate();
  const month =
    dateObj.getMonth() + 1 < 10
      ? `0${dateObj.getMonth() + 1}`
      : dateObj.getMonth() + 1;
  const year = dateObj.getFullYear();

  if (showTime) {
    let hours = dateObj.getHours();
    const minutes =
      dateObj.getMinutes() < 10
        ? `0${dateObj.getMinutes()}`
        : dateObj.getMinutes();

    if (is12HourFormat) {
      const period = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12 || 12; // Convert to 12-hour format (0 becomes 12)
      return `${year}/${month}/${day} ${hours}:${minutes} ${period}`;
    }

    return `${year}/${month}/${day} ${hours}:${minutes}`; // 24-hour format
  }

  return `${year}/${month}/${day}`; // Date only
};
export const formatTransactionType = (type: number) => {
  switch (type) {
    case 1:
      return 'Deposit';
    case 2:
      return 'Withdraw';
    case 3:
      return 'Transfer';
    default:
      return '';
  }
};

const syncUpdateApp = (downloadProgress: any) => {
  return new Promise((resolve: any, reject) => {
    CodePush.sync(
      {
        installMode: CodePush.InstallMode.IMMEDIATE,
      },
      syncStatus => {
        if (
          syncStatus === CodePush.SyncStatus.UPDATE_INSTALLED ||
          syncStatus === CodePush.SyncStatus.UP_TO_DATE
        ) {
          CodePush.notifyAppReady();
          // if (Platform.OS === 'ios') {
          //   RNRestart.restart();
          // }
          resolve();
        }
        if (syncStatus === CodePush.SyncStatus.UNKNOWN_ERROR) {
          reject();
        }
      },
      progress => {
        const {receivedBytes, totalBytes} = progress;
        const percent = ((receivedBytes / totalBytes) * 100).toFixed(1);
        console.log(`Progress: ${percent}% - ${receivedBytes}/${totalBytes}`);
        downloadProgress(Number(percent));
      },
    );
  });
};

export const updateAppWithCodepush = (
  downloadProgress: (percentComplete: number) => void = () => {},
  onStartUpdate: () => void = () => {},
) => {
  return new Promise((resolve: any) => {
    try {
      CodePush.checkForUpdate()
        .then(async update => {
          console.log('CHECK UPDATE CODEPUSH', update);
          if (update) {
            if (update.failedInstall) {
              CodePush.clearUpdates();
            }

            onStartUpdate(); // 🟡 Gọi trước để trigger UI
            await new Promise(res => setTimeout(res, 200)); // ⏳ Tạo độ trễ nhẹ để UI kịp cập nhật
            await syncUpdateApp(downloadProgress);
          }

          resolve();
        })
        .catch(error => {
          console.error('Error checking for update:', error);
          resolve();
        });
    } catch (error) {
      console.error('Error in updateAppWithCodepush:', error);
      resolve();
    }
  });
};
