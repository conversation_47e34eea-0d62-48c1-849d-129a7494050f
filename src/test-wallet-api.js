// Test file for Wallet API
// This file can be used to test the wallet API integration

import { api } from './api';

// Test function to call wallet API
export const testWalletAPI = async () => {
  try {
    console.log('Testing Wallet API...');
    
    // Test with no parameters
    const response1 = await api.getWallets();
    console.log('Response without params:', response1);
    
    // Test with network and crypto parameters
    const response2 = await api.getWallets({
      network: 'trc20',
      crypto: 'usdt',
      page: 1,
      limit: 10
    });
    console.log('Response with params:', response2);
    
    return response2;
  } catch (error) {
    console.error('Wallet API test failed:', error);
    throw error;
  }
};

// Example usage:
// testWalletAPI().then(data => console.log('Success:', data));
