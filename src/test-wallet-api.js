// Test file for Wallet API
// This file can be used to test the wallet API integration

import {api} from './api';

// Test function to call wallet API
export const testWalletAPI = async () => {
  try {
    console.log('Testing Wallet API...');

    // Test with no parameters
    const response1 = await api.getWallets();
    console.log('Response without params:', response1);

    // Test with network and crypto parameters
    const response2 = await api.getWallets({
      network: 'trc20',
      crypto: 'usdt',
      system: 'binance',
      page: 1,
      limit: 10,
    });
    console.log('Response with params:', response2);

    return response2;
  } catch (error) {
    console.error('Wallet API test failed:', error);
    throw error;
  }
};

// Test function to call wallet filter APIs
export const testWalletFilterAPIs = async () => {
  try {
    console.log('Testing Wallet Filter APIs...');

    // Test systems API
    const systemsResponse = await api.getWalletSystems();
    console.log('Systems Response:', systemsResponse);

    // Test networks API
    const networksResponse = await api.getWalletNetworks();
    console.log('Networks Response:', networksResponse);

    // Test cryptos API
    const cryptosResponse = await api.getWalletCryptos();
    console.log('Cryptos Response:', cryptosResponse);

    return {
      systems: systemsResponse,
      networks: networksResponse,
      cryptos: cryptosResponse,
    };
  } catch (error) {
    console.error('Wallet Filter APIs test failed:', error);
    throw error;
  }
};

// Example usage:
// testWalletAPI().then(data => console.log('Success:', data));
// testWalletFilterAPIs().then(data => console.log('Filter APIs Success:', data));
