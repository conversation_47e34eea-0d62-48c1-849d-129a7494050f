import {View, StyleSheet} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {scale} from 'utils/device';
import Spinner from 'react-native-spinkit';
import {useGlobalContext} from 'context/GlobalContext';
import {colors} from 'assets';

export default function GlobalLoading() {
  const {isLoading} = useGlobalContext();
  return (
    <Modal
      backdropColor={colors.black}
      style={styles.container}
      isVisible={isLoading}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      animationInTiming={200}
      animationOutTiming={200}
      useNativeDriverForBackdrop={true}
      hideModalContentWhileAnimating={true}>
      {/* <View style={styles.body}> */}
      <Spinner
        isVisible={true}
        size={50}
        type={'FadingCircleAlt'}
        color={colors.white}
      />
      {/* </View> */}
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  body: {
    backgroundColor: colors.black2,
    height: scale(100),
    width: scale(100),
    borderRadius: scale(20),
    alignItems: 'center',
    justifyContent: 'center',
  },
});
