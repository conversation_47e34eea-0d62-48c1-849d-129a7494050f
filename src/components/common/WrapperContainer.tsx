import {colors} from 'assets';
import React from 'react';
import {StatusBar, StatusBarStyle, StyleSheet, ViewStyle} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

type Props = {
  children: React.ReactNode;
  style?: ViewStyle;
  styleStatusBar?: ViewStyle;
  barStyle?: StatusBarStyle;
  isFooter?: boolean;
  styleFooter?: ViewStyle;
};

export default function WrapperContainer({
  children,
  style,
  styleStatusBar = {},
  barStyle = 'light-content',
  isFooter = false,
  styleFooter = {},
}: Props) {
  return (
    <SafeAreaView edges={['top']} style={[styles.container, style]}>
      <StatusBar
        animated
        backgroundColor={styleStatusBar.backgroundColor || colors.primary}
        barStyle={barStyle}
        {...styleStatusBar}
      />
      {children}
      {isFooter && (
        <SafeAreaView style={[styles.footer, styleFooter]} edges={['bottom']} />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray1,
  },
  footer: {
    backgroundColor: '#FFFFFF',
  },
});
