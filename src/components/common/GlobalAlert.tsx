import React from 'react';
import {Text, View, StyleSheet, TouchableOpacity} from 'react-native';
import Modal from 'react-native-modal';
import {scale} from 'utils/device';
import {colors, fonts} from 'assets';
import {useGlobalContext} from 'context/GlobalContext';

const GlobalAlert = () => {
  const {
    isModalVisible,
    handleModal,
    modalProps: {message, type, onSubmit, onCancel},
  } = useGlobalContext();

  const getTitleByType = (type: string) => {
    switch (type) {
      case 'success':
        return 'Success';
      case 'error':
        return 'Error';
      case 'info':
        return 'Thông tin';
      case 'custom':
        return 'Alert';
      case 'message':
        return 'Thông báo';
    }
  };

  const onClose = () => {
    if (onCancel) onCancel();
    handleModal(false);
  };

  const onOK = () => {
    if (onSubmit) onSubmit();
    handleModal(false);
  };

  return (
    <Modal
      backdropOpacity={0.5}
      useNativeDriver={true} // <PERSON><PERSON>i thiện hiệu suất animation
      hideModalContentWhileAnimating={true}
      style={styles.container}
      isVisible={isModalVisible}>
      <View style={styles.body}>
        <Text style={styles.txtTitle}>{getTitleByType(type)}</Text>
        <Text style={styles.txtDes}>{message}</Text>
        {onSubmit ? (
          <View style={styles.row}>
            <TouchableOpacity
              onPress={onClose}
              style={[styles.btnSubmitLine, styles.marginRight]}>
              <Text style={[styles.txtText, styles.textCancel]}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.btnSubmit} onPress={onOK}>
              <Text style={styles.txtText}>OK</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.row}>
            <TouchableOpacity style={styles.btnSubmit} onPress={onOK}>
              <Text style={styles.txtText}>OK</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  body: {
    backgroundColor: colors.gray2,
    borderRadius: scale(20),
    width: scale(300),
    paddingVertical: scale(20),
    paddingHorizontal: scale(24),
    alignItems: 'center',
  },
  txtTitle: {
    fontFamily: fonts.SFPro.bold,
    fontSize: scale(20),
    color: colors.white,
  },
  txtDes: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(16),
    marginTop: scale(20),
    color: colors.white,
  },
  row: {
    flexDirection: 'row',
    marginTop: scale(24),
  },
  btnSubmit: {
    height: scale(50),
    backgroundColor: colors.yellow,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(90),
  },
  btnSubmitLine: {
    height: scale(50),
    borderColor: '#D6DAE1',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(90),
    flex: 1,
  },
  marginRight: {
    marginRight: scale(10),
  },
  txtText: {
    fontFamily: fonts.SFPro.bold,
    fontSize: scale(16),
    color: colors.black,
  },
  textCancel: {
    color: '#D6DAE1',
  },
});

export default GlobalAlert;
