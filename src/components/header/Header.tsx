import {useNavigation} from '@react-navigation/native';
import {colors, fonts, images} from 'assets';
import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {useSelector} from 'react-redux';
import {scale} from 'utils/device';

function Header() {
  const navigation = useNavigation<any>();
  const {login} = useSelector((state: any) => state.user);

  const onPress = () => {
    navigation.openDrawer();
  };

  return (
    <View style={styles.container}>
      <Image
        source={
          login?.data?.avatar ? {uri: login?.data?.avatar} : images.logo_tiger
        }
        style={styles.avatar}
      />
      <View style={styles.info}>
        <Text style={styles.welcome}>Welcome back,</Text>
        <Text style={styles.username}>{login?.data?.name}</Text>
      </View>
      <TouchableOpacity style={styles.msgBtn}>
        <Image source={images.ic_message} style={styles.iconMessage} />
      </TouchableOpacity>
      <TouchableOpacity onPress={onPress} style={styles.menuButton}>
        <Image source={images.ic_menu} style={styles.menuIcon} />
      </TouchableOpacity>
    </View>
  );
}

export default Header;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: scale(20),
    alignItems: 'center',
    paddingBottom: scale(15),
  },
  avatar: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(25),
  },
  info: {
    flex: 1,
    paddingLeft: scale(10),
  },
  welcome: {
    fontFamily: fonts.SFPro.regular,
    fontSize: scale(15),
    color: colors.white,
  },
  username: {
    fontFamily: fonts.SFPro.bold,
    fontSize: scale(18),
    color: '#F48A33',
  },
  msgBtn: {
    backgroundColor: colors.black,
    padding: scale(8),
    borderRadius: scale(5),
    borderWidth: 1,
    borderColor: '#1B1B1B',
  },
  iconMessage: {
    width: scale(24),
    height: scale(24),
  },
  menuButton: {
    padding: scale(10),
  },
  menuIcon: {
    width: scale(24),
    height: scale(24),
  },
});
