import {colors, fonts, images} from 'assets';
import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {scale} from 'utils/device';

type Props = {
  title?: string;
  message?: string;
  closeBottomSheet: () => void;
};

function AlertSuccess({title = '', message = '', closeBottomSheet}: Props) {
  return (
    <View style={styles.container}>
      <Image source={images.ic_success} style={styles.icSuccess} />
      {title && (
        <View>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.successfully}>Successfully</Text>
        </View>
      )}
      {message && <Text style={styles.title}>{message}</Text>}
      <TouchableOpacity onPress={closeBottomSheet} style={styles.goBackBtn}>
        <Text style={styles.goBackTxt}>Go Back</Text>
      </TouchableOpacity>
    </View>
  );
}

export default AlertSuccess;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  icSuccess: {
    width: scale(90),
    height: scale(90),
    marginTop: scale(160),
  },
  title: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(30),
    color: colors.white,
    marginTop: scale(30),
    paddingHorizontal: scale(30),
  },
  successfully: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(35),
    color: colors.yellow,
  },
  goBackBtn: {
    width: scale(100),
    height: scale(30),
    backgroundColor: colors.yellow,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(5),
    marginTop: scale(93),
  },
  goBackTxt: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(15),
    color: colors.black,
  },
});
