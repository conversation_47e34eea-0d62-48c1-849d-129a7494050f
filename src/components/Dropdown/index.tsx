import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Image,
  Animated,
} from 'react-native';
import {scale} from 'utils/device';
import {colors, fonts, images} from 'assets';

interface DropdownItem {
  id?: string;
  code?: string;
  name: string;
  [key: string]: any;
}

interface Props {
  data: DropdownItem[];
  selectedValue: DropdownItem | string;
  onSelect: (value: DropdownItem) => void;
  containerStyle?: object;
  zIndex?: number;
  isYellow?: boolean;
  labelKey?: string;
}

const Dropdown: React.FC<Props> = ({
  data,
  selectedValue,
  onSelect,
  containerStyle,
  zIndex = 1000,
  isYellow = true,
  labelKey = 'name',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownHeight = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const getDropdownHeight = () => {
      const itemHeight = scale(44);
      const calculatedHeight = data.length * itemHeight;
      if (calculatedHeight < scale(150)) {
        return scale(150);
      }
      return Math.min(calculatedHeight, scale(300));
    };

    const height = getDropdownHeight();
    Animated.timing(dropdownHeight, {
      toValue: isOpen ? height : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isOpen, dropdownHeight, data.length]);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleSelect = (item: DropdownItem) => {
    onSelect(item);
    setIsOpen(false);
  };

  const renderItem = ({item}: {item: DropdownItem}) => (
    <TouchableOpacity
      style={styles.item}
      onPress={() => handleSelect(item)}>
      <Text style={styles.itemText}>{item[labelKey]}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, containerStyle, {zIndex}]}>
      <TouchableOpacity
        style={styles.selector}
        onPress={toggleDropdown}
        activeOpacity={0.7}>
        <Text style={[styles.selectedText, isYellow && {color: colors.yellow}]}>
          {typeof selectedValue === 'string'
            ? selectedValue
            : selectedValue[labelKey]}
        </Text>
        <Image
          source={images.ic_dropdown}
          style={[
            styles.dropdownIcon,
            {transform: [{rotate: isOpen ? '180deg' : '0deg'}]},
          ]}
        />
      </TouchableOpacity>

      <Animated.View
        style={[
          styles.dropdown,
          {
            height: dropdownHeight,
            opacity: dropdownHeight.interpolate({
              inputRange: [0, 50],
              outputRange: [0, 1],
            }),
          },
        ]}>
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={item => item.code || item.id || item[labelKey]}
          bounces={false}
          showsVerticalScrollIndicator={true}
          nestedScrollEnabled={true}
          style={styles.flatList}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: scale(10),
  },
  selectedText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
  dropdownIcon: {
    width: scale(12),
    height: scale(12),
    opacity: 0.5,
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: colors.black,
    borderRadius: scale(8),
    borderWidth: 1,
    borderColor: colors.gray_border,
    overflow: 'hidden',
    zIndex: 1000,
  },
  flatList: {
    flexGrow: 0,
  },
  item: {
    padding: scale(12),
    borderBottomWidth: 0.5,
    borderBottomColor: colors.gray_border,
    height: scale(44),
  },
  itemText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
});

export default Dropdown;
