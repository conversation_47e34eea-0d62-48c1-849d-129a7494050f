import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Image,
  Animated,
} from 'react-native';
import {scale} from 'utils/device';
import {colors, fonts, images} from 'assets';

interface CountryCode {
  id: string;
  name: string;
  code: string;
  dial_code: string;
  [key: string]: any;
}

interface Props {
  data: CountryCode[];
  selectedValue: CountryCode | string;
  onSelect: (value: CountryCode) => void;
  containerStyle?: object;
  zIndex?: number;
  isYellow?: boolean;
  labelKey?: string;
  valueKey?: string;
}

const CountryCodePicker: React.FC<Props> = ({
  data,
  selectedValue,
  onSelect,
  containerStyle,
  zIndex = 1000,
  isYellow = true,
  labelKey = 'name',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownHeight = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const getDropdownHeight = () => {
      const itemHeight = scale(44);
      const calculatedHeight = data.length * itemHeight;
      if (calculatedHeight < scale(150)) {
        return scale(150);
      }
      return Math.min(calculatedHeight, scale(300));
    };

    const height = getDropdownHeight();
    Animated.timing(dropdownHeight, {
      toValue: isOpen ? height : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isOpen, dropdownHeight, data.length]);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleSelect = (item: CountryCode) => {
    onSelect(item);
    setIsOpen(false);
  };

  const renderItem = ({item}: {item: CountryCode}) => (
    <TouchableOpacity
      style={styles.countryItem}
      onPress={() => handleSelect(item)}>
      <Text style={styles.countryText}>{item[labelKey]}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, containerStyle, {zIndex}]}>
      <TouchableOpacity
        style={styles.selector}
        onPress={toggleDropdown}
        activeOpacity={0.7}>
        <Text style={[styles.selectedCode, isYellow && {color: colors.yellow}]}>
          {typeof selectedValue === 'string'
            ? selectedValue
            : selectedValue[labelKey]}
        </Text>
        <Image
          source={images.ic_dropdown}
          style={[
            styles.dropdownIcon,
            {transform: [{rotate: isOpen ? '180deg' : '0deg'}]},
          ]}
        />
      </TouchableOpacity>

      <Animated.View
        style={[
          styles.dropdown,
          {
            height: dropdownHeight,
            opacity: dropdownHeight.interpolate({
              inputRange: [0, 50],
              outputRange: [0, 1],
            }),
          },
        ]}>
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          bounces={false}
          showsVerticalScrollIndicator={true}
          nestedScrollEnabled={true}
          style={styles.flatList}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(8),
    paddingVertical: scale(8),
    paddingHorizontal: scale(4),
  },
  selectedCode: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
  dropdownIcon: {
    width: scale(12),
    height: scale(12),
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: colors.black,
    borderRadius: scale(8),
    borderWidth: 1,
    borderColor: colors.gray_border,
    overflow: 'hidden',
    zIndex: 1000,
    width: scale(100),
  },
  flatList: {
    flexGrow: 0,
  },
  countryItem: {
    flexDirection: 'row',
    padding: scale(12),
    borderBottomWidth: 0.5,
    borderBottomColor: colors.gray_border,
    gap: scale(4),
  },
  countryText: {
    color: colors.white,
    fontSize: scale(15),
    fontFamily: fonts.SFPro.regular,
  },
});

export default CountryCodePicker;
