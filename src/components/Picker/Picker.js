import React from 'react';
import {
  FlatList,
  Image,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import Modal from 'react-native-modal';

import {colors, fonts, images} from 'assets';
import {scale} from 'utils/device';

export default class CPicker extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalVisible: false,
      StrFind: '',
      dataCombo: [],
      dataSelect: [],
      type: null,
    };
    this.dataTemp = null;
  }

  show = (data, type) => {
    const {Mode, items} = this.props;
    //mode = 1: select multiple ---- mode = 0: select single
    if (Mode === 1) {
      this.setState({
        dataSelect: data && data.length > 0 ? data : [],
        dataCombo: items,
      });
      this.dataTemp = data ? [...data] : [];
    } else {
      this.setState({
        dataSelect:
          data && data.label && (data.value || data.value === 0)
            ? [
                {
                  label: data.label,
                  value: data.value,
                },
              ]
            : [],
        dataCombo: items,
      });
    }
    this.setState({
      isModalVisible: true,
      type: type,
    });
  };

  close = isData => {
    this.setState({
      isModalVisible: false,
      dataCombo: [],
    });
    if (!isData && this.props.Mode === 1) {
      this.props.onValueChange && this.props.onValueChange(this.dataTemp);
    }
  };

  onSave = () => {
    const {Mode} = this.props;
    const {dataSelect, type} = this.state;
    if (type && type === 'VIEW') {
      return;
    }
    if (Mode === 1) {
      this.props.onValueChange(dataSelect);
      this.close(true);
    } else {
      if (dataSelect.length > 0) {
        this.props.onValueChange({
          label: dataSelect[0].label,
          value: dataSelect[0].value,
        });
      } else {
        this.props.onValueChange(null);
      }
      this.close(true);
    }
  };

  onClear = () => {
    this.inputRef && this.inputRef.setNativeProps({text: ''});
    this.setState({
      dataCombo: this.props.items,
    });
  };

  onSelectCombo = item => {
    const {Mode} = this.props;
    const {dataSelect, type} = this.state;
    if (type && type === 'VIEW') {
      return;
    }
    let data = dataSelect;
    if (Mode === 1) {
      const findItem = dataSelect.findIndex(e => e.value === item.value);
      if (findItem > -1) {
        data.splice(findItem, 1);
        this.setState({
          dataSelect: data,
        });
      } else {
        data.push(item);
        this.setState({
          dataSelect: data,
        });
      }
    } else {
      data = [];
      data.push(item);
      this.setState({
        dataSelect: data,
      });
    }
  };

  onSearch = text => {
    const {items} = this.props;
    if (!text) {
      this.setState({
        dataCombo: items,
      });
    }
    if (text && items) {
      const dataTemp = items.filter(i => {
        return (
          this.nonAccentVietnamese(i.label).includes(
            this.nonAccentVietnamese(text),
          ) ||
          this.nonAccentVietnamese(i.value).includes(
            this.nonAccentVietnamese(text),
          )
        );
      });
      this.setState({
        dataCombo: dataTemp,
      });
    }
  };

  //xoa khang cach va dau cua chuoi
  nonAccentVietnamese = str => {
    if (str) {
      str = str.toString();
      str = str.toLowerCase();
      str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
      str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
      str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
      str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
      str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
      str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
      str = str.replace(/đ/g, 'd');
      str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // Huyền sắc hỏi ngã nặng
      str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // Â, Ê, Ă, Ơ, Ư
      str = str.replace(/[^0-9a-z_\-\s#*./(\\)]/gi, '');
      return str;
    }
    return '';
  };

  componentWillReceiveProps(nextProps, nextContext) {
    if (nextProps.items !== this.props.items) {
      this.setState({
        dataCombo: nextProps.items ? nextProps.items : [],
      });
    }
  }

  renderCombo = ({item, index}) => {
    const {Mode, isShowValue, renderLabel, itemContainer} = this.props;
    const findItem = this.state.dataSelect.findIndex(
      e => e.value === item.value,
    );
    const {theme} = this.context;
    return (
      <TouchableOpacity onPress={() => this.onSelectCombo(item)}>
        <View
          style={[
            styles.item,
            {backgroundColor: theme.picker.backgroundColor},
            itemContainer,
          ]}>
          {Mode === 1 ? (
            <View style={styles.rowComboItem}>
              <Image
                source={
                  findItem > -1
                    ? images.ic_radio_checked
                    : images.ic_radio_uncheck
                }
                style={{
                  height: scale(18),
                  width: scale(18),
                  marginLeft: scale(16),
                  marginRight: scale(8),
                  tintColor: colors.white,
                }}
              />
              {renderLabel ? (
                renderLabel(item, index)
              ) : (
                <View style={{flexDirection: 'row', flex: 1}}>
                  <Text style={[styles.txtItem, {color: theme.picker.title}]}>
                    {item.label ? item.label : ''}
                  </Text>
                  {isShowValue && (
                    <Text style={styles.txtItem}>
                      {' '}
                      - {item.value ? item.value : ''}
                    </Text>
                  )}
                </View>
              )}
            </View>
          ) : (
            <View style={styles.rowComboItem}>
              <Image
                source={
                  findItem > -1
                    ? images.ic_radio_checked
                    : images.ic_radio_uncheck
                }
                style={{
                  height: scale(18),
                  width: scale(18),
                  marginLeft: scale(16),
                  marginRight: scale(8),
                  tintColor: colors.white,
                }}
              />
              {renderLabel ? (
                renderLabel(item, index)
              ) : (
                <View style={{flexDirection: 'row', flex: 1}}>
                  <Text style={[styles.txtItem, {color: theme.picker.title}]}>
                    {item.label ? item.label : ''}
                  </Text>
                  {isShowValue && (
                    <Text style={styles.txtItem}>
                      {' '}
                      - {item.value ? item.value : ''}
                    </Text>
                  )}
                </View>
              )}
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  onDeSelect = () => {
    this.setState({
      dataSelect: [],
    });
  };

  render() {
    const {isModalVisible, dataCombo, dataSelect} = this.state;
    const {title, cancelable, Mode, contentContainerStyle, preventDeselect} =
      this.props;
    const {theme} = this.context;
    return (
      <Modal
        onBackdropPress={cancelable ? () => this.close(false) : null}
        useNativeDriver
        style={styles.modalContainer}
        hideModalContentWhileAnimating
        isVisible={isModalVisible}>
        <KeyboardAvoidingView
          alwaysBounceVertical={false}
          keyboardVerticalOffset={scale(50)}
          behavior={Platform.OS === 'android' ? undefined : 'position'}>
          <View
            style={[
              styles.container,
              {backgroundColor: theme.picker.backgroundColor},
              contentContainerStyle,
            ]}>
            <View style={styles.header}>
              <Text style={styles.txtHeader}>
                {title ? title.toUpperCase() : ''}
              </Text>
            </View>
            <View style={styles.search}>
              <Image
                source={images.ic_search}
                style={{
                  height: scale(16),
                  width: scale(16),
                  tintColor: colors.white,
                }}
              />
              <TextInput
                ref={ref => (this.inputRef = ref)}
                style={[styles.input, {color: theme.picker.title}]}
                placeholder={''}
                onChangeText={this.onSearch}
              />
              <TouchableOpacity onPress={this.onClear}>
                <Image
                  source={images.ic_close}
                  style={{
                    height: scale(14),
                    width: scale(14),
                    tintColor: colors.white,
                  }}
                />
              </TouchableOpacity>
            </View>
            <View style={styles.listCombo}>
              <FlatList
                style={{maxHeight: scale(192)}}
                data={dataCombo}
                renderItem={this.renderCombo}
                alwaysBounceVertical={false}
                keyExtractor={(item, index) => index.toString()}
                extraData={this.state}
              />
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent:
                  !preventDeselect &&
                  dataSelect &&
                  dataSelect.length > 0 &&
                  Mode !== 1
                    ? 'space-between'
                    : 'flex-end',
                marginTop: scale(14),
                marginHorizontal: scale(18),
              }}>
              {!preventDeselect &&
                dataSelect &&
                dataSelect.length > 0 &&
                Mode !== 1 && (
                  <TouchableOpacity
                    style={{marginRight: scale(16), padding: scale(10)}}
                    onPress={this.onDeSelect}>
                    <Text style={[styles.txtChon, {color: '#6E6E6E'}]}>
                      Deselect
                    </Text>
                  </TouchableOpacity>
                )}
              <View style={{flexDirection: 'row'}}>
                <TouchableOpacity
                  style={{marginRight: scale(25), padding: scale(10)}}
                  onPress={() => this.close(false)}>
                  <Text style={styles.txtHuy}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{padding: scale(10)}}
                  onPress={this.onSave}>
                  <Text style={styles.txtChon}>{localize.select}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    );
  }
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    margin: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: scale(360 - 44),
    backgroundColor: '#FFFFFF',
    borderRadius: scale(4),
    paddingBottom: scale(10),
  },
  header: {
    height: scale(50),
    justifyContent: 'center',
    marginLeft: scale(16),
  },
  txtHeader: {
    fontSize: scale(16),
    lineHeight: scale(20),
    color: colors.cF1BD12,
    fontFamily: fonts.SFPro.bold,
  },
  search: {
    height: scale(32),
    marginHorizontal: scale(16),
    borderWidth: scale(1),
    borderRadius: scale(6),
    marginBottom: scale(10),
    borderColor: '#6E6E6E',
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: scale(10),
  },
  listCombo: {
    minHeight: scale(192),
  },
  item: {
    width: '100%',
    minHeight: scale(34),
    flexDirection: 'row',
    alignItems: 'center',
  },
  txtItem: {
    fontSize: scale(16),
    lineHeight: scale(20),
    color: '#1B1B1B',
    fontFamily: fonts.SFPro.regular,
  },
  txtHuy: {
    fontSize: scale(16),
    lineHeight: scale(20),
    color: '#6E6E6E',
    fontFamily: fonts.SFPro.bold,
  },
  txtChon: {
    fontSize: scale(16),
    lineHeight: scale(20),
    color: colors.cF1BD12,
    fontFamily: fonts.SFPro.bold,
  },
  rowComboItem: {
    flexDirection: 'row',
    width: '100%',
    marginBottom: scale(5),
  },
  input: {
    marginLeft: scale(9),
    flex: 1,
    fontSize: scale(14),
    fontFamily: fonts.SFPro.regular,
    paddingRight: scale(5),
    paddingVertical: 0,
  },
});
