const colors = {
  primary: '#0161A8',
  yellow: '#F58A32',
  white: '#FFFFFF',
  black: '#0F0F14',
  black2: '#0F141A',
  inactiveIcon: '#747376',
  grayBorder: '#28272C',
  gray1: '#2A3440',
  gray_border: '#D9D9D9',
  gray_button: '#1B1B1B',
  gray2: '#1C242F',
  green: '#39E73E',
  red: '#D20000',
  gray_text: '#B3B3B3',
  weekend_date: '#17A1FA',
  background: '#0F141A',
};

const fonts = {
  SFPro: {
    bold: 'SFProDisplay-Bold',
    regular: 'SFProDisplay-Regular',
    semiBold: 'SFProDisplay-Semibold',
    medium: 'SFProDisplay-Medium',
    italic: 'SFProDisplay-RegularItalic',
  },
  IBMPlexSans: {
    medium: 'IBMPlexSansCondensed-Medium',
  },
};

const images = {
  ic_home_inactive: require('./images/ic_home.png'),
  ic_transaction_inactive: require('./images/ic_transaction.png'),
  ic_tools_inactive: require('./images/ic_tools.png'),
  ic_profile_inactive: require('./images/ic_profile.png'),
  ic_refer_active: require('./images/ic_refer.png'),
  ic_promotion_active: require('./images/ic_promotion.png'),
  ic_contact_active: require('./images/ic_contact.png'),
  ic_announcement_active: require('./images/ic_announcement.png'),
  ic_menu: require('./images/ic_menu.png'),
  ic_signout: require('./images/ic_signout.png'),
  ic_close: require('./images/ic_close.png'),
  ic_logo_google: require('./images/ic_logo_google.webp'),
  ic_logo_facebook: require('./images/ic_logo_facebook.webp'),
  ic_dropdown: require('./images/ic_dropdown.webp'),
  ic_back: require('./images/ic_back.png'),
  ic_avatar: require('./images/ic_avatar.png'),
  ic_message: require('./images/ic_message.png'),
  account_balance_background: require('./images/account_balance_background.png'),
  logo_tiger: require('./images/logo_tiger.png'),
  ic_deposit: require('./images/ic_deposit.png'),
  ic_withdraw: require('./images/ic_withdraw.png'),
  ic_transfer: require('./images/ic_transfer.png'),
  forex_ads: require('./images/forex_ads.png'),
  gema_awards: require('./images/gema_awards.png'),
  ic_copy: require('./images/ic_copy.png'),
  ic_qrcode: require('./images/ic_qrcode.png'),
  ic_more: require('./images/ic_more.png'),
  ic_account_create_success: require('./images/ic_account_create_success.webp'),
  ic_arrow_right: require('./images/ic_arrow_right.png'),
  ic_arrow_down: require('./images/ic_arrow_down.png'),
  ic_success: require('./images/ic_success.png'),
  ic_verified: require('./images/ic_verified.webp'),
  ic_upload_file: require('./images/ic_upload_file.webp'),
  ic_check: require('./images/ic_check.webp'),
  ic_eye_off: require('./images/ic_eye_off.webp'),
  ic_phone_authenticator: require('./images/ic_phone_authenticator.webp'),
  ic_email_authenticator: require('./images/ic_email_authenticator.webp'),
  ic_switch: require('./images/ic_switch.webp'),
  ic_switch_active: require('./images/ic_switch_active.webp'),
  ic_pdf_file: require('./images/ic_pdf_file.webp'),
  ic_search: require('./images/ic_search.png'),
  logo_news: require('./images/logo_news.png'),
  ic_calendar: require('./images/ic_calendar.png'),
  tools_image: require('./images/tools_image.png'),
  ic_address: require('./images/ic_address.png'),
  ic_phone: require('./images/ic_phone.png'),
  ic_email: require('./images/ic_email.png'),
  ic_radio_checked: require('./images/ic_radio_checked.png'),
  ic_radio_uncheck: require('./images/ic_radio_uncheck.png'),
};

export {colors, fonts, images};
