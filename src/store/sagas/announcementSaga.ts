import {api} from 'api';

import {SagaIterator} from 'redux-saga';
import {call, put, takeLatest} from 'redux-saga/effects';
import {announcementActions} from 'store';
import {formatError} from 'utils/errorHandler';

function* getAnnouncementSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getAnnouncement, action.payload);
    yield put(announcementActions.getAnnouncementSuccess(response));
  } catch (error: any) {
    yield put(announcementActions.getAnnouncementFailed(formatError(error)));
  }
}

export default function* announcementSaga() {
  yield takeLatest(announcementActions.getAnnouncement, getAnnouncementSaga);
}
