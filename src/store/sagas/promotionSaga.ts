import {api} from 'api';

import {SagaIterator} from 'redux-saga';
import {call, put, takeLatest} from 'redux-saga/effects';
import {promotionActions} from 'store';
import {formatError} from 'utils/errorHandler';

function* getPromotionSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getPromotion, action.payload);
    yield put(promotionActions.getPromotionSuccess(response));
  } catch (error: any) {
    yield put(promotionActions.getPromotionFailed(formatError(error)));
  }
}

export default function* promotionSaga() {
  yield takeLatest(promotionActions.getPromotion, getPromotionSaga);
}
