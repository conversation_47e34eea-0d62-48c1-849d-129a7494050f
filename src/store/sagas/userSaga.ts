import { call, put, takeLatest } from 'redux-saga/effects';
import { userActions } from 'store';
import { api } from 'api';
import { SagaIterator } from 'redux-saga';
import { formatError } from 'utils/errorHandler';

function* getRegisterSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.register, action.payload);
    yield put(userActions.getRegisterSuccess(response));
  } catch (error: any) {
    yield put(userActions.getRegisterFailed(formatError(error)));
  }
}

function* loginSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.login, action.payload);
    yield put(userActions.loginSuccess(response));
  } catch (error: any) {
    yield put(userActions.loginFailed(formatError(error)));
  }
}

function* getCountrySaga(): SagaIterator {
  try {
    const response = yield call(api.getCountry);
    yield put(userActions.getCountrySuccess(response));
  } catch (error: any) {
    yield put(userActions.getCountryFailed(formatError(error)));
  }
}

function* forgotUsernameSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.forgotUsername, action.payload);
    yield put(userActions.forgotUsernameSuccess(response));
  } catch (error: any) {
    yield put(userActions.forgotUsernameFailed(formatError(error)));
  }
}

function* getTotalBalanceSaga(): SagaIterator {
  try {
    const response = yield call(api.getTotalBalance);
    yield put(userActions.getTotalBalanceSuccess(response));
  } catch (error: any) {
    yield put(userActions.getTotalBalanceFailed(formatError(error)));
  }
}

export default function* userSaga() {
  yield takeLatest(userActions.getRegister, getRegisterSaga);
  yield takeLatest(userActions.login, loginSaga);
  yield takeLatest(userActions.getCountry, getCountrySaga);
  yield takeLatest(userActions.forgotUsername, forgotUsernameSaga);
  yield takeLatest(userActions.getTotalBalance, getTotalBalanceSaga);
}
