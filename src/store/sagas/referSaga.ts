import {api} from 'api';

import {SagaIterator} from 'redux-saga';
import {call, put, takeLatest} from 'redux-saga/effects';
import {referActions} from 'store/reducers/refer/actions';
import {formatError} from 'utils/errorHandler';

function* getMyClientsSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getMyIBClients, action.payload);
    yield put(referActions.getMyClientsSuccess(response));
  } catch (error: any) {
    yield put(referActions.getMyClientsFailed(formatError(error)));
  }
}

function* getMIBClientsSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getMIBClients, action.payload);
    yield put(referActions.getMIBClientsSuccess(response));
  } catch (error: any) {
    yield put(referActions.getMIBClientsFailed(formatError(error)));
  }
}

function* getMyIBAndMIBSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getMyIBAndMIB, action.payload);
    yield put(referActions.getMyIBAndMIBSuccess(response));
  } catch (error: any) {
    yield put(referActions.getMyIBAndMIBFailed(formatError(error)));
  }
}

function* getIBsUnderSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getIBsUnder, action.payload);
    yield put(referActions.getIBsUnderSuccess(response));
  } catch (error: any) {
    yield put(referActions.getIBsUnderFailed(formatError(error)));
  }
}

function* getIBRequestSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getIBRequest, action.payload);
    yield put(referActions.getIBRequestSuccess(response));
  } catch (error: any) {
    yield put(referActions.getIBRequestFailed(formatError(error)));
  }
}

function* createIBRequestSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.createIBRequest, action.payload);
    yield put(referActions.createIBRequestSuccess(response));
  } catch (error: any) {
    yield put(referActions.createIBRequestFailed(formatError(error)));
  }
}

function* getTradeHistorySaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getTradeHistory, action.payload);
    yield put(referActions.getTradeHistorySuccess(response));
  } catch (error: any) {
    yield put(referActions.getTradeHistoryFailed(formatError(error)));
  }
}

function* getTotalProfitSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getTotalProfit, action.payload);
    yield put(referActions.getTotalProfitSuccess(response));
  } catch (error: any) {
    yield put(referActions.getTotalProfitFailed(formatError(error)));
  }
}

function* getTotalVolumeSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getTotalVolume, action.payload);
    yield put(referActions.getTotalVolumeSuccess(response));
  } catch (error: any) {
    yield put(referActions.getTotalVolumeFailed(formatError(error)));
  }
}

function* getCommissionHistorySaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getCommissionHistory, action.payload);
    yield put(referActions.getCommissionHistorySuccess(response));
  } catch (error: any) {
    yield put(referActions.getCommissionHistoryFailed(formatError(error)));
  }
}

export default function* referSaga() {
  yield takeLatest(referActions.getMyClients, getMyClientsSaga);
  yield takeLatest(referActions.getMIBClients, getMIBClientsSaga);
  yield takeLatest(referActions.getMyIBAndMIB, getMyIBAndMIBSaga);
  yield takeLatest(referActions.getIBsUnder, getIBsUnderSaga);
  yield takeLatest(referActions.getIBRequest, getIBRequestSaga);
  yield takeLatest(referActions.createIBRequest, createIBRequestSaga);
  yield takeLatest(referActions.getTradeHistory, getTradeHistorySaga);
  yield takeLatest(referActions.getTotalProfit, getTotalProfitSaga);
  yield takeLatest(referActions.getTotalVolume, getTotalVolumeSaga);
  yield takeLatest(referActions.getCommissionHistory, getCommissionHistorySaga);
}
