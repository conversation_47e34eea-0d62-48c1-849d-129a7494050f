import {api} from 'api';

import {SagaIterator} from 'redux-saga';
import {call, put, takeLatest} from 'redux-saga/effects';
import {contactActions} from 'store';
import {formatError} from 'utils/errorHandler';

function* sendContactSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.sendContact, action.payload);
    yield put(contactActions.sendContactSuccess(response));
  } catch (error: any) {
    yield put(contactActions.sendContactFailed(formatError(error)));
  }
}

export default function* contactSaga() {
  yield takeLatest(contactActions.sendContact, sendContactSaga);
}
