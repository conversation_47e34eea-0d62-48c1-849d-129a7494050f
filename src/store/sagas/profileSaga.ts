import {call, put, takeLatest} from 'redux-saga/effects';
import {profileActions} from 'store';
import {api} from 'api';
import {SagaIterator} from 'redux-saga';
import {formatError} from 'utils/errorHandler';

function* getLegalDocumentsSaga(): SagaIterator {
  try {
    const response = yield call(api.getLegalDocuments);
    yield put(profileActions.getLegalDocumentsSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getLegalDocumentsFailed(formatError(error)));
  }
}

function* getProfileSaga(): SagaIterator {
  try {
    const response = yield call(api.getProfile);
    console.log('CHECK PROFILE RESPONSE:', response);
    yield put(profileActions.getProfileSuccess(response));
  } catch (error: any) {
    console.log('CHECK PROFILE ERROR:', error);
    yield put(profileActions.getProfileFailed(formatError(error)));
  }
}

function* getWalletsSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getWallets, action.payload);
    yield put(profileActions.getWalletsSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getWalletsFailed(formatError(error)));
  }
}

export default function* profileSaga() {
  yield takeLatest(profileActions.getLegalDocuments, getLegalDocumentsSaga);
  yield takeLatest(profileActions.getProfile, getProfileSaga);
  yield takeLatest(profileActions.getWallets, getWalletsSaga);
}
