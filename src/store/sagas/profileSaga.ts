import {call, put, takeLatest} from 'redux-saga/effects';
import {profileActions} from 'store';
import {api} from 'api';
import {SagaIterator} from 'redux-saga';
import {formatError} from 'utils/errorHandler';

function* getLegalDocumentsSaga(): SagaIterator {
  try {
    const response = yield call(api.getLegalDocuments);
    yield put(profileActions.getLegalDocumentsSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getLegalDocumentsFailed(formatError(error)));
  }
}

function* getProfileSaga(): SagaIterator {
  try {
    const response = yield call(api.getProfile);
    console.log('CHECK PROFILE RESPONSE:', response);
    yield put(profileActions.getProfileSuccess(response));
  } catch (error: any) {
    console.log('CHECK PROFILE ERROR:', error);
    yield put(profileActions.getProfileFailed(formatError(error)));
  }
}

function* getWalletsSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getWallets, action.payload);
    yield put(profileActions.getWalletsSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getWalletsFailed(formatError(error)));
  }
}

function* getWalletSystemsSaga(): SagaIterator {
  try {
    const response = yield call(api.getWalletSystems);
    yield put(profileActions.getWalletSystemsSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getWalletSystemsFailed(formatError(error)));
  }
}

function* getWalletNetworksSaga(): SagaIterator {
  try {
    const response = yield call(api.getWalletNetworks);
    yield put(profileActions.getWalletNetworksSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getWalletNetworksFailed(formatError(error)));
  }
}

function* getWalletCryptosSaga(): SagaIterator {
  try {
    const response = yield call(api.getWalletCryptos);
    yield put(profileActions.getWalletCryptosSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getWalletCryptosFailed(formatError(error)));
  }
}

function* createWalletSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.createWallet, action.payload);
    yield put(profileActions.createWalletSuccess(response));
    yield put(profileActions.getWallets({}));
  } catch (error: any) {
    yield put(profileActions.createWalletFailed(formatError(error)));
  }
}

function* updateWalletSaga(action: any): SagaIterator {
  try {
    const {id, ...data} = action.payload;
    const response = yield call(api.updateWallet, id, data);
    yield put(profileActions.updateWalletSuccess(response));
    yield put(profileActions.getWallets({}));
  } catch (error: any) {
    yield put(profileActions.updateWalletFailed(formatError(error)));
  }
}

function* getBankAccountsSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getBankAccounts, action.payload);
    yield put(profileActions.getBankAccountsSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getBankAccountsFailed(formatError(error)));
  }
}

function* getBankCountriesSaga(): SagaIterator {
  try {
    const response = yield call(api.getBankCountries);
    yield put(profileActions.getBankCountriesSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getBankCountriesFailed(formatError(error)));
  }
}

function* getBankNamesSaga(): SagaIterator {
  try {
    const response = yield call(api.getBankNames);
    yield put(profileActions.getBankNamesSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getBankNamesFailed(formatError(error)));
  }
}

function* getBankCurrenciesSaga(): SagaIterator {
  try {
    const response = yield call(api.getBankCurrencies);
    yield put(profileActions.getBankCurrenciesSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getBankCurrenciesFailed(formatError(error)));
  }
}

function* getCurrencySaga(): SagaIterator {
  try {
    const response = yield call(api.getCurrency);
    yield put(profileActions.getCurrencySuccess(response));
  } catch (error: any) {
    yield put(profileActions.getCurrencyFailed(formatError(error)));
  }
}

function* getBankCreateAccountSaga(): SagaIterator {
  try {
    const response = yield call(api.getBankCreateAccount);
    yield put(profileActions.getBankCreateAccountSuccess(response));
  } catch (error: any) {
    yield put(profileActions.getBankCreateAccountFailed(formatError(error)));
  }
}

function* getGoogle2FASaga(): SagaIterator {
  try {
    const response = yield call(api.getGoogle2FA);
    yield put(profileActions.getGoogle2FASuccess(response));
  } catch (error: any) {
    yield put(profileActions.getGoogle2FAFailed(formatError(error)));
  }
}

export default function* profileSaga() {
  yield takeLatest(profileActions.getLegalDocuments, getLegalDocumentsSaga);
  yield takeLatest(profileActions.getProfile, getProfileSaga);
  yield takeLatest(profileActions.getWallets, getWalletsSaga);
  yield takeLatest(profileActions.createWallet, createWalletSaga);
  yield takeLatest(profileActions.updateWallet, updateWalletSaga);
  yield takeLatest(profileActions.getWalletSystems, getWalletSystemsSaga);
  yield takeLatest(profileActions.getWalletNetworks, getWalletNetworksSaga);
  yield takeLatest(profileActions.getWalletCryptos, getWalletCryptosSaga);
  yield takeLatest(profileActions.getBankAccounts, getBankAccountsSaga);
  yield takeLatest(profileActions.getBankCountries, getBankCountriesSaga);
  yield takeLatest(profileActions.getBankNames, getBankNamesSaga);
  yield takeLatest(profileActions.getBankCurrencies, getBankCurrenciesSaga);
  yield takeLatest(profileActions.getCurrency, getCurrencySaga);
  yield takeLatest(
    profileActions.getBankCreateAccount,
    getBankCreateAccountSaga,
  );
  yield takeLatest(profileActions.getGoogle2FA, getGoogle2FASaga);
}
