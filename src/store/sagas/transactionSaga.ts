import { api } from 'api';

import { SagaIterator } from 'redux-saga';
import { call, put, takeEvery } from 'redux-saga/effects';
import { transactionActions } from 'store';
import { formatError } from 'utils/errorHandler';

function* getTransactionSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getTransaction, action.payload);
    switch (action.payload.type) {
      case 0:
        yield put(transactionActions.getTransactionSuccess(response));
        break;
      case 1:
        yield put(transactionActions.getDepositSuccess(response));
        break;
      case 2:
        yield put(transactionActions.getWithdrawSuccess(response));
        break;
      case 3:
        yield put(transactionActions.getTransferSuccess(response));
        break;
      default:
        yield put(transactionActions.getTransactionSuccess(response));
        break;
    }
  } catch (error: any) {
    yield put(transactionActions.getTransactionFailed(formatError(error)));
  }
}

function* getMethodSaga(action: any): SagaIterator {
  try {
    const response = yield call(api.getMethod, action.payload);
    switch (action.payload.type) {
      case 1:
        yield put(transactionActions.getDepositMethodSuccess(response));
        break;
      case 3:
        yield put(transactionActions.getWithdrawMethodSuccess(response));
        break;
    }
  } catch (error: any) {
    switch (action.payload.type) {
      case 1:
        yield put(transactionActions.getDepositMethodFailed(formatError(error)));
        break;
      case 3:
        yield put(transactionActions.getWithdrawFailed(formatError(error)));

    }
  }
}

export default function* transactionSaga() {
  yield takeEvery(transactionActions.getTransaction, getTransactionSaga);
  yield takeEvery(transactionActions.getDeposit, getTransactionSaga);
  yield takeEvery(transactionActions.getWithdraw, getTransactionSaga);
  yield takeEvery(transactionActions.getTransfer, getTransactionSaga);
  yield takeEvery(transactionActions.getDepositMethod, getMethodSaga);
  yield takeEvery(transactionActions.getWithdrawMethod, getMethodSaga);
}
