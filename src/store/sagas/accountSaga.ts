import { api } from 'api';

import { SagaIterator } from 'redux-saga';
import { call, put, takeLatest } from 'redux-saga/effects';
import { accountActions, announcementActions } from 'store';
import { formatError } from 'utils/errorHandler';

function* getAccountSaga(action: any): SagaIterator {
    try {
        const response = yield call(api.getAccount, action.payload);
        yield put(accountActions.getAccountSuccess(response));
    } catch (error: any) {
        yield put(accountActions.getAccountFailed(formatError(error)));
    }
}

export default function* accountSaga() {
    yield takeLatest(accountActions.getAccount, getAccountSaga);
}
