import { fork as pureFork, all, call } from 'redux-saga/effects';
import { Saga } from 'redux-saga';
import userSaga from './userSaga';
import profileSaga from './profileSaga';
import logService from 'utils/logService';
import contactSaga from './contactSaga';
import promotionSaga from './promotionSaga';
import announcementSaga from './announcementSaga';
import transactionSaga from './transactionSaga';
import referSaga from './referSaga';
import accountSaga from './accountSaga';

function withErrorBoundary(saga: Saga) {
  return function* wrappedSaga(...args: any[]) {
    try {
      yield call(saga, ...args);
    } catch (error) {
      console.error(`${saga.name} error: `, error);
      logService.captureException(error, { saga: saga.name });
    }
  };
}

const fork = (saga: Saga) => pureFork(withErrorBoundary(saga));

const sagas = [
  userSaga,
  profileSaga,
  contactSaga,
  promotionSaga,
  announcementSaga,
  transactionSaga,
  referSaga,
  accountSaga
];

function* rootSaga() {
  yield all(sagas.map(fork));
}

export default rootSaga;
