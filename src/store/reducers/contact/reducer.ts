import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  sendContact: {
    loading: false,
    data: null,
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
};

const contactSlice = createSlice({
  name: 'contact',
  initialState,
  reducers: {
    sendContact: (state, action) => {
      state.sendContact = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    sendContactSuccess: (state, action) => {
      state.sendContact.loading = false;
      state.sendContact.data = action.payload.data;
      state.sendContact.success = true;
      state.sendContact.message = action.payload.message;
    },
    sendContactFailed: (state, action) => {
      state.sendContact.loading = false;
      state.sendContact.error = true;
      state.sendContact.message = action.payload.message;
    },
  },
});

export const {sendContact, sendContactSuccess, sendContactFailed} =
  contactSlice.actions;
export default contactSlice.reducer;
