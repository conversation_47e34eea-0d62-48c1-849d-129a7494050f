import { createSlice } from '@reduxjs/toolkit';

interface AccountState {
    getAccount: {
        loading: boolean;
        data: any[];
        error: boolean | null;
        message: string;
        success: boolean | null;
        page: number;
        last_page: number;
    };
}

const initialState = {
    getAccount: {
        loading: false,
        data: [],
        error: null as boolean | null,
        message: '',
        success: null as boolean | null,
        page: 1,
        last_page: 1,
    },
};

const accountSlice = createSlice({
    name: 'account',
    initialState,
    reducers: {
        getAccount: (state: AccountState, action) => {
            state.getAccount.loading = true;
        },
        getAccountSuccess: (state: AccountState, action) => {
            state.getAccount.loading = false;
            if (action.payload.data.page === 1) {
                state.getAccount.data = action.payload.data.content;
            } else if (action.payload.data.page > 1) {
                state.getAccount.data = [
                    ...state.getAccount.data,
                    ...action.payload.data.content,
                ];
            }
            state.getAccount.success = true;
            state.getAccount.page = action.payload?.data?.page;
            state.getAccount.last_page = action.payload?.data?.last_page;
        },
        getAccountFailed: (state: AccountState, action) => {
            state.getAccount.loading = false;
            state.getAccount.error = true;
            state.getAccount.message = action.payload.message;
        },
        getAccountUpdatePage: (state: AccountState, action) => {
            state.getAccount.page = action.payload;
        },
        resetAccount: (state: AccountState) => {
            state.getAccount = initialState.getAccount;
        },
    },
});

export const {
    getAccount,
    getAccountSuccess,
    getAccountFailed,
    getAccountUpdatePage,
    resetAccount,
} = accountSlice.actions;
export default accountSlice.reducer;