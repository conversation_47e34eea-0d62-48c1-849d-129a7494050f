import {createSlice} from '@reduxjs/toolkit';

interface AnnouncementState {
  getAnnouncement: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
  };
}

const initialState = {
  getAnnouncement: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
  },
};

const announcementSlice = createSlice({
  name: 'announcement',
  initialState,
  reducers: {
    getAnnouncement: (state: AnnouncementState, action) => {
      state.getAnnouncement.loading = true;
    },
    getAnnouncementSuccess: (state: AnnouncementState, action) => {
      state.getAnnouncement.loading = false;
      if (action.payload.data.page === 1) {
        state.getAnnouncement.data = action.payload.data.content;
      } else if (action.payload.data.page > 1) {
        state.getAnnouncement.data = [
          ...state.getAnnouncement.data,
          ...action.payload.data.content,
        ];
      }
      state.getAnnouncement.success = true;
      state.getAnnouncement.page = action.payload?.data?.page;
      state.getAnnouncement.last_page = action.payload?.data?.last_page;
    },
    getAnnouncementFailed: (state: AnnouncementState, action) => {
      state.getAnnouncement.loading = false;
      state.getAnnouncement.error = true;
      state.getAnnouncement.message = action.payload.message;
    },
    getAnnouncementUpdatePage: (state: AnnouncementState, action) => {
      state.getAnnouncement.page = action.payload;
    },
    resetAnnouncement: (state: AnnouncementState) => {
      state.getAnnouncement = initialState.getAnnouncement;
    },
  },
});

export const {
  getAnnouncement,
  getAnnouncementSuccess,
  getAnnouncementFailed,
  getAnnouncementUpdatePage,
  resetAnnouncement,
} = announcementSlice.actions;
export default announcementSlice.reducer;
