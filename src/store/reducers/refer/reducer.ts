import {createSlice} from '@reduxjs/toolkit';

interface ReferState {
  getMyClients: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
    total: number;
  };
  getMIBClients: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
    total: number;
  };
  getMyIBAndMIB: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
    total: number;
  };
  getIBsUnder: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
    total: number;
  };
  getIBRequest: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
    total: number;
  };
  createIBRequest: {
    loading: boolean;
    data: any;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  getTradeHistory: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
    total: number;
  };
  getTotalProfit: {
    loading: boolean;
    data: number | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  getTotalVolume: {
    loading: boolean;
    data: number | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
}

const initialState = {
  getMyClients: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
    total: 0,
  },
  getMIBClients: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
    total: 0,
  },
  getMyIBAndMIB: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
    total: 0,
  },
  getIBsUnder: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
    total: 0,
  },
  getIBRequest: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
    total: 0,
  },
  createIBRequest: {
    loading: false,
    data: null,
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
  getTradeHistory: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
    total: 0,
  },
  getTotalProfit: {
    loading: false,
    data: null,
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
  getTotalVolume: {
    loading: false,
    data: null,
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
};

const referSlice = createSlice({
  name: 'refer',
  initialState,
  reducers: {
    // My Clients reducers
    getMyClients: (state, _action) => {
      state.getMyClients.loading = true;
    },
    getMyClientsSuccess: (state, action) => {
      state.getMyClients.loading = false;
      if (action.payload.data.page === 1) {
        state.getMyClients.data = action.payload.data.content;
      } else if (action.payload.data.page > 1) {
        state.getMyClients.data = [
          ...state.getMyClients.data,
          ...action.payload.data.content,
        ];
      }
      state.getMyClients.success = true;
      state.getMyClients.page = action.payload?.data?.page;
      state.getMyClients.last_page = action.payload?.data?.last_page;
      state.getMyClients.total = action.payload?.data?.total;
    },
    getMyClientsFailed: (state, action) => {
      state.getMyClients.loading = false;
      state.getMyClients.error = true;
      state.getMyClients.message = action.payload.message;
    },
    getMyClientsUpdatePage: (state, action) => {
      state.getMyClients.page = action.payload;
    },
    resetMyClients: state => {
      state.getMyClients = initialState.getMyClients;
    },

    // MIB Clients reducers
    getMIBClients: (state, _action) => {
      state.getMIBClients.loading = true;
    },
    getMIBClientsSuccess: (state, action) => {
      state.getMIBClients.loading = false;
      if (action.payload.data.page === 1) {
        state.getMIBClients.data = action.payload.data.content;
      } else if (action.payload.data.page > 1) {
        state.getMIBClients.data = [
          ...state.getMIBClients.data,
          ...action.payload.data.content,
        ];
      }
      state.getMIBClients.success = true;
      state.getMIBClients.page = action.payload?.data?.page;
      state.getMIBClients.last_page = action.payload?.data?.last_page;
      state.getMIBClients.total = action.payload?.data?.total;
    },
    getMIBClientsFailed: (state, action) => {
      state.getMIBClients.loading = false;
      state.getMIBClients.error = true;
      state.getMIBClients.message = action.payload.message;
    },
    getMIBClientsUpdatePage: (state, action) => {
      state.getMIBClients.page = action.payload;
    },
    resetMIBClients: state => {
      state.getMIBClients = initialState.getMIBClients;
    },

    // My IB & MIB reducers
    getMyIBAndMIB: (state, _action) => {
      state.getMyIBAndMIB.loading = true;
    },
    getMyIBAndMIBSuccess: (state, action) => {
      state.getMyIBAndMIB.loading = false;
      if (action.payload.data.page === 1) {
        state.getMyIBAndMIB.data = action.payload.data.content;
      } else if (action.payload.data.page > 1) {
        state.getMyIBAndMIB.data = [
          ...state.getMyIBAndMIB.data,
          ...action.payload.data.content,
        ];
      }
      state.getMyIBAndMIB.success = true;
      state.getMyIBAndMIB.page = action.payload?.data?.page;
      state.getMyIBAndMIB.last_page = action.payload?.data?.last_page;
      state.getMyIBAndMIB.total = action.payload?.data?.total;
    },
    getMyIBAndMIBFailed: (state, action) => {
      state.getMyIBAndMIB.loading = false;
      state.getMyIBAndMIB.error = true;
      state.getMyIBAndMIB.message = action.payload.message;
    },
    getMyIBAndMIBUpdatePage: (state, action) => {
      state.getMyIBAndMIB.page = action.payload;
    },
    resetMyIBAndMIB: state => {
      state.getMyIBAndMIB = initialState.getMyIBAndMIB;
    },

    // IBs Under reducers
    getIBsUnder: (state, _action) => {
      state.getIBsUnder.loading = true;
    },
    getIBsUnderSuccess: (state, action) => {
      state.getIBsUnder.loading = false;
      if (action.payload.data.page === 1) {
        state.getIBsUnder.data = action.payload.data.content;
      } else if (action.payload.data.page > 1) {
        state.getIBsUnder.data = [
          ...state.getIBsUnder.data,
          ...action.payload.data.content,
        ];
      }
      state.getIBsUnder.success = true;
      state.getIBsUnder.page = action.payload?.data?.page;
      state.getIBsUnder.last_page = action.payload?.data?.last_page;
      state.getIBsUnder.total = action.payload?.data?.total;
    },
    getIBsUnderFailed: (state, action) => {
      state.getIBsUnder.loading = false;
      state.getIBsUnder.error = true;
      state.getIBsUnder.message = action.payload.message;
    },
    getIBsUnderUpdatePage: (state, action) => {
      state.getIBsUnder.page = action.payload;
    },
    resetIBsUnder: state => {
      state.getIBsUnder = initialState.getIBsUnder;
    },

    // IB Request reducers
    getIBRequest: (state, _action) => {
      state.getIBRequest.loading = true;
    },
    getIBRequestSuccess: (state, action) => {
      state.getIBRequest.loading = false;
      if (action.payload.data.page === 1) {
        state.getIBRequest.data = action.payload.data.content;
      } else if (action.payload.data.page > 1) {
        state.getIBRequest.data = [
          ...state.getIBRequest.data,
          ...action.payload.data.content,
        ];
      }
      state.getIBRequest.success = true;
      state.getIBRequest.page = action.payload?.data?.page;
      state.getIBRequest.last_page = action.payload?.data?.last_page;
      state.getIBRequest.total = action.payload?.data?.total;
    },
    getIBRequestFailed: (state, action) => {
      state.getIBRequest.loading = false;
      state.getIBRequest.error = true;
      state.getIBRequest.message = action.payload.message;
    },
    getIBRequestUpdatePage: (state, action) => {
      state.getIBRequest.page = action.payload;
    },
    resetIBRequest: state => {
      state.getIBRequest = initialState.getIBRequest;
    },

    // Create IB Request reducers
    createIBRequest: (state, _action) => {
      state.createIBRequest.loading = true;
      state.createIBRequest.error = null;
      state.createIBRequest.success = null;
    },
    createIBRequestSuccess: (state, action) => {
      state.createIBRequest.loading = false;
      state.createIBRequest.data = action.payload.data;
      state.createIBRequest.success = true;
    },
    createIBRequestFailed: (state, action) => {
      state.createIBRequest.loading = false;
      state.createIBRequest.error = true;
      state.createIBRequest.message = action.payload.message;
    },
    resetCreateIBRequest: state => {
      state.createIBRequest = initialState.createIBRequest;
    },

    // Trade History reducers
    getTradeHistory: (state, _action) => {
      state.getTradeHistory.loading = true;
    },
    getTradeHistorySuccess: (state, action) => {
      state.getTradeHistory.loading = false;
      if (action.payload.data.page === 1) {
        state.getTradeHistory.data = action.payload.data.content;
      } else if (action.payload.data.page > 1) {
        state.getTradeHistory.data = [
          ...state.getTradeHistory.data,
          ...action.payload.data.content,
        ];
      }
      state.getTradeHistory.success = true;
      state.getTradeHistory.page = action.payload?.data?.page;
      state.getTradeHistory.last_page = action.payload?.data?.last_page;
      state.getTradeHistory.total = action.payload?.data?.total;
    },
    getTradeHistoryFailed: (state, action) => {
      state.getTradeHistory.loading = false;
      state.getTradeHistory.error = true;
      state.getTradeHistory.message = action.payload.message;
    },
    getTradeHistoryUpdatePage: (state, action) => {
      state.getTradeHistory.page = action.payload;
    },
    resetTradeHistory: state => {
      state.getTradeHistory = initialState.getTradeHistory;
    },

    // Total Profit reducers
    getTotalProfit: (state, _action) => {
      state.getTotalProfit.loading = true;
    },
    getTotalProfitSuccess: (state, action) => {
      state.getTotalProfit.loading = false;
      state.getTotalProfit.data = action.payload.data;
      state.getTotalProfit.success = true;
    },
    getTotalProfitFailed: (state, action) => {
      state.getTotalProfit.loading = false;
      state.getTotalProfit.error = true;
      state.getTotalProfit.message = action.payload.message;
    },
    resetTotalProfit: state => {
      state.getTotalProfit = initialState.getTotalProfit;
    },

    // Total Volume reducers
    getTotalVolume: (state, _action) => {
      state.getTotalVolume.loading = true;
    },
    getTotalVolumeSuccess: (state, action) => {
      state.getTotalVolume.loading = false;
      state.getTotalVolume.data = action.payload.data;
      state.getTotalVolume.success = true;
    },
    getTotalVolumeFailed: (state, action) => {
      state.getTotalVolume.loading = false;
      state.getTotalVolume.error = true;
      state.getTotalVolume.message = action.payload.message;
    },
    resetTotalVolume: state => {
      state.getTotalVolume = initialState.getTotalVolume;
    },
  },
});

export const {
  getMyClients,
  getMyClientsSuccess,
  getMyClientsFailed,
  getMyClientsUpdatePage,
  resetMyClients,
  getMIBClients,
  getMIBClientsSuccess,
  getMIBClientsFailed,
  getMIBClientsUpdatePage,
  resetMIBClients,
  getMyIBAndMIB,
  getMyIBAndMIBSuccess,
  getMyIBAndMIBFailed,
  getMyIBAndMIBUpdatePage,
  resetMyIBAndMIB,
  getIBsUnder,
  getIBsUnderSuccess,
  getIBsUnderFailed,
  getIBsUnderUpdatePage,
  resetIBsUnder,
  getIBRequest,
  getIBRequestSuccess,
  getIBRequestFailed,
  getIBRequestUpdatePage,
  resetIBRequest,
  createIBRequest,
  createIBRequestSuccess,
  createIBRequestFailed,
  resetCreateIBRequest,
  getTradeHistory,
  getTradeHistorySuccess,
  getTradeHistoryFailed,
  getTradeHistoryUpdatePage,
  resetTradeHistory,
  getTotalProfit,
  getTotalProfitSuccess,
  getTotalProfitFailed,
  resetTotalProfit,
  getTotalVolume,
  getTotalVolumeSuccess,
  getTotalVolumeFailed,
  resetTotalVolume,
} = referSlice.actions;
export default referSlice.reducer;
