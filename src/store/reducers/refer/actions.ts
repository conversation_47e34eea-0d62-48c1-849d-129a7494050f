import {
  getMyClients,
  getMyClientsSuccess,
  getMyClientsFailed,
  getMyClientsUpdatePage,
  resetMyClients,
  getMIBClients,
  getMIBClientsSuccess,
  getMIBClientsFailed,
  getMIBClientsUpdatePage,
  resetMIBClients,
  getMyIBAndMIB,
  getMyIBAndMIBSuccess,
  getMyIBAndMIBFailed,
  getMyIBAndMIBUpdatePage,
  resetMyIBAndMIB,
  getIBsUnder,
  getIBsUnderSuccess,
  getIBsUnderFailed,
  getIBsUnderUpdatePage,
  resetIBsUnder,
  getIBRequest,
  getIBRequestSuccess,
  getIBRequestFailed,
  getIBRequestUpdatePage,
  resetIBRequest,
  createIBRequest,
  createIBRequestSuc<PERSON>,
  createIBRequestFailed,
  resetCreateIBRequest,
  getTradeHistory,
  getTradeHistorySuccess,
  getTradeHistoryFailed,
  getTradeHistoryUpdatePage,
  resetTradeHistory,
  getTotalProfit,
  getTotalProfitSuccess,
  getTotalProfitFailed,
  resetTotalProfit,
  getTotalVolume,
  getTotalVolumeSuccess,
  getTotalVolumeFailed,
  resetTotalVolume,
  getCommissionHistory,
  getCommissionHistorySuccess,
  getCommissionHistoryFailed,
  getCommissionHistoryUpdatePage,
  resetCommissionHistory,
} from './reducer';

export const referActions = {
  getMyClients,
  getMyClientsSuccess,
  getMyClientsFailed,
  getMyClientsUpdatePage,
  resetMyClients,
  getMIBClients,
  getMIBClientsSuccess,
  getMIBClientsFailed,
  getMIBClientsUpdatePage,
  resetMIBClients,
  getMyIBAndMIB,
  getMyIBAndMIBSuccess,
  getMyIBAndMIBFailed,
  getMyIBAndMIBUpdatePage,
  resetMyIBAndMIB,
  getIBsUnder,
  getIBsUnderSuccess,
  getIBsUnderFailed,
  getIBsUnderUpdatePage,
  resetIBsUnder,
  getIBRequest,
  getIBRequestSuccess,
  getIBRequestFailed,
  getIBRequestUpdatePage,
  resetIBRequest,
  createIBRequest,
  createIBRequestSuccess,
  createIBRequestFailed,
  resetCreateIBRequest,
  getTradeHistory,
  getTradeHistorySuccess,
  getTradeHistoryFailed,
  getTradeHistoryUpdatePage,
  resetTradeHistory,
  getTotalProfit,
  getTotalProfitSuccess,
  getTotalProfitFailed,
  resetTotalProfit,
  getTotalVolume,
  getTotalVolumeSuccess,
  getTotalVolumeFailed,
  resetTotalVolume,
  getCommissionHistory,
  getCommissionHistorySuccess,
  getCommissionHistoryFailed,
  getCommissionHistoryUpdatePage,
  resetCommissionHistory,
};
