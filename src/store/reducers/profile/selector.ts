import {RootState} from 'store/types';

export const selectLegalDocuments = (state: RootState) =>
  state.profile.legalDocuments;
export const selectProfileInfo = (state: RootState) =>
  state.profile.profileInfo;

export const selectWallets = (state: RootState) => state.profile.wallets;

export const selectCreateWallet = (state: RootState) =>
  state.profile.createWallet;

export const selectUpdateWallet = (state: RootState) =>
  state.profile.updateWallet;

export const selectWalletSystems = (state: RootState) =>
  state.profile.walletSystems;

export const selectWalletNetworks = (state: RootState) =>
  state.profile.walletNetworks;

export const selectWalletCryptos = (state: RootState) =>
  state.profile.walletCryptos;
