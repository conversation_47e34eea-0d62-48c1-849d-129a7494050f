import {createSlice} from '@reduxjs/toolkit';

interface LegalDocument {
  name: string;
  icon: string;
  url: string;
}

interface ProfileData {
  id: number;
  username: string;
  name: string;
  email: string;
  has_verified_email: boolean;
  has_verified_phone: boolean;
  has_verified_twofactor: boolean;
  token_2fa_expiry: string;
  is_enabled_authentication: boolean;
  status: number;
  is_verify: number;
  birthday: string;
  phone: string;
  identify: string;
  agent_account: number;
  agent_user_id: number;
  avatar: string;
  identify_front: string;
  identify_back: string;
  passport_front: string;
  passport_back: string;
  driver_front: string;
  driver_back: string;
  address_front: string;
  provider: string;
  provider_id: string;
  address: string;
  district: string;
  city: string;
  state: string;
  country: string;
  country_code: string;
  pcode: string;
  created_at: string;
  dial_code: string;
  country_code_by_dial: string;
  register_type: number;
  support_password: string;
  token: string;
  is_completed_basic_info: boolean;
  is_uploaded_dentify: boolean;
  is_uploaded_identify: boolean;
  is_uploaded_address: boolean;
  register_channel: boolean;
  code: string;
  can_external_transfer: number;
  verify_type: number;
  can_join_contest: boolean;
  ib_refer_code: string | null;
}

interface ProfileState {
  legalDocuments: {
    loading: boolean;
    data: LegalDocument[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  profileInfo: {
    loading: boolean;
    data: ProfileData | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
}

const initialState: ProfileState = {
  legalDocuments: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  profileInfo: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    getLegalDocuments: state => {
      state.legalDocuments = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getLegalDocumentsSuccess: (state, action) => {
      state.legalDocuments.loading = false;
      state.legalDocuments.data = action.payload.data;
      state.legalDocuments.success = true;
    },
    getLegalDocumentsFailed: (state, action) => {
      state.legalDocuments.loading = false;
      state.legalDocuments.error = true;
      state.legalDocuments.message = action.payload.message;
    },
    getProfile: state => {
      state.profileInfo = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getProfileSuccess: (state, action) => {
      state.profileInfo.loading = false;
      state.profileInfo.data = action.payload.data;
      state.profileInfo.success = true;
    },
    getProfileFailed: (state, action) => {
      state.profileInfo.loading = false;
      state.profileInfo.error = true;
      state.profileInfo.message = action.payload.message;
    },
  },
});

export const {
  getLegalDocuments,
  getLegalDocumentsSuccess,
  getLegalDocumentsFailed,
  getProfile,
  getProfileSuccess,
  getProfileFailed,
} = profileSlice.actions;

export default profileSlice.reducer;
