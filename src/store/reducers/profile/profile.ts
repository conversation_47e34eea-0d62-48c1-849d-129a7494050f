import {createSlice} from '@reduxjs/toolkit';

interface LegalDocument {
  name: string;
  icon: string;
  url: string;
}

interface Wallet {
  id: number;
  name: string;
  address: string;
  network: string;
  system: string;
  crypto: string;
  status: string;
  created_at: string;
}

interface WalletFilter {
  code: string;
  name: string;
}

interface BankAccount {
  id: number;
  logo: string;
  name: string;
  swift_code: string;
  iban: string;
  holder_name: string;
  account: string;
  branch: string;
  country: string;
  currency: string;
  created_at: string;
  qr_code_image: string;
  qr_code_url: string | null;
}

interface BankFilter {
  code: string;
  name: string;
}

interface Currency {
  id: number;
  name: string;
  code: string;
  conversion: string;
  deposit_exchange_rate: number;
  withdraw_exchange_rate: number;
  created_at: string;
  updated_at: string;
}

interface BankName {
  codeAndName: string;
  name: string;
  short: string;
  swift: string;
}

interface Google2FAData {
  qr_image: string;
  secret: string;
}

interface ProfileData {
  id: number;
  username: string;
  name: string;
  email: string;
  has_verified_email: boolean;
  has_verified_phone: boolean;
  has_verified_twofactor: boolean;
  token_2fa_expiry: string;
  is_enabled_authentication: boolean;
  status: number;
  is_verify: number;
  birthday: string;
  phone: string;
  identify: string;
  agent_account: number;
  agent_user_id: number;
  avatar: string;
  identify_front: string;
  identify_back: string;
  passport_front: string;
  passport_back: string;
  driver_front: string;
  driver_back: string;
  address_front: string;
  provider: string;
  provider_id: string;
  address: string;
  district: string;
  city: string;
  state: string;
  country: string;
  country_code: string;
  pcode: string;
  created_at: string;
  dial_code: string;
  country_code_by_dial: string;
  register_type: number;
  support_password: string;
  token: string;
  is_completed_basic_info: boolean;
  is_uploaded_dentify: boolean;
  is_uploaded_identify: boolean;
  is_uploaded_address: boolean;
  register_channel: boolean;
  code: string;
  can_external_transfer: number;
  verify_type: number;
  can_join_contest: boolean;
  ib_refer_code: string | null;
}

interface ProfileState {
  legalDocuments: {
    loading: boolean;
    data: LegalDocument[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  profileInfo: {
    loading: boolean;
    data: ProfileData | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  wallets: {
    loading: boolean;
    data: Wallet[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
    pagination: {
      page: number;
      last_page: number;
      per_page: number;
      total: number;
    } | null;
  };
  createWallet: {
    loading: boolean;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  updateWallet: {
    loading: boolean;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  walletSystems: {
    loading: boolean;
    data: WalletFilter[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  walletNetworks: {
    loading: boolean;
    data: WalletFilter[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  walletCryptos: {
    loading: boolean;
    data: WalletFilter[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  bankAccounts: {
    loading: boolean;
    data: BankAccount[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
    pagination: {
      page: number;
      last_page: number;
      per_page: number;
      total: number;
    } | null;
  };
  bankCountries: {
    loading: boolean;
    data: BankFilter[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  bankNames: {
    loading: boolean;
    data: BankFilter[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  bankCurrencies: {
    loading: boolean;
    data: BankFilter[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  currency: {
    loading: boolean;
    data: Currency[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  bankCreateAccount: {
    loading: boolean;
    data: BankName[] | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
  google2FA: {
    loading: boolean;
    data: Google2FAData | null;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
}

const initialState: ProfileState = {
  legalDocuments: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  profileInfo: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  wallets: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
    pagination: null,
  },
  createWallet: {
    loading: false,
    error: null,
    message: '',
    success: null,
  },
  updateWallet: {
    loading: false,
    error: null,
    message: '',
    success: null,
  },
  walletSystems: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  walletNetworks: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  walletCryptos: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  bankAccounts: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
    pagination: null,
  },
  bankCountries: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  bankNames: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  bankCurrencies: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  currency: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  bankCreateAccount: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
  google2FA: {
    loading: false,
    data: null,
    error: null,
    message: '',
    success: null,
  },
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    getLegalDocuments: state => {
      state.legalDocuments = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getLegalDocumentsSuccess: (state, action) => {
      state.legalDocuments.loading = false;
      state.legalDocuments.data = action.payload.data;
      state.legalDocuments.success = true;
    },
    getLegalDocumentsFailed: (state, action) => {
      state.legalDocuments.loading = false;
      state.legalDocuments.error = true;
      state.legalDocuments.message = action.payload.message;
    },
    getProfile: state => {
      state.profileInfo = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getProfileSuccess: (state, action) => {
      state.profileInfo.loading = false;
      state.profileInfo.data = action.payload.data;
      state.profileInfo.success = true;
    },
    getProfileFailed: (state, action) => {
      state.profileInfo.loading = false;
      state.profileInfo.error = true;
      state.profileInfo.message = action.payload.message;
    },
    getWallets: state => {
      state.wallets = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
        pagination: null,
      };
    },
    getWalletsSuccess: (state, action) => {
      state.wallets.loading = false;
      state.wallets.data = action.payload.data.content;
      state.wallets.success = true;
      state.wallets.pagination = {
        page: action.payload.data.page,
        last_page: action.payload.data.last_page,
        per_page: action.payload.data.per_page,
        total: action.payload.data.total,
      };
    },
    getWalletsFailed: (state, action) => {
      state.wallets.loading = false;
      state.wallets.error = true;
      state.wallets.message = action.payload.message;
    },
    createWallet: (state, action) => {
      state.createWallet = {
        loading: true,
        error: null,
        message: '',
        success: null,
      };
    },
    createWalletSuccess: (state, action) => {
      state.createWallet.loading = false;
      state.createWallet.success = true;
      state.createWallet.message =
        action.payload.message || 'Wallet created successfully';
    },
    createWalletFailed: (state, action) => {
      state.createWallet.loading = false;
      state.createWallet.error = true;
      state.createWallet.message = action.payload.message;
    },
    updateWallet: (state, action) => {
      state.updateWallet = {
        loading: true,
        error: null,
        message: '',
        success: null,
      };
    },
    updateWalletSuccess: (state, action) => {
      state.updateWallet.loading = false;
      state.updateWallet.success = true;
      state.updateWallet.message =
        action.payload.message || 'Wallet updated successfully';
    },
    updateWalletFailed: (state, action) => {
      state.updateWallet.loading = false;
      state.updateWallet.error = true;
      state.updateWallet.message = action.payload.message;
    },
    getWalletSystems: state => {
      state.walletSystems = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getWalletSystemsSuccess: (state, action) => {
      state.walletSystems.loading = false;
      state.walletSystems.data = action.payload.data;
      state.walletSystems.success = true;
    },
    getWalletSystemsFailed: (state, action) => {
      state.walletSystems.loading = false;
      state.walletSystems.error = true;
      state.walletSystems.message = action.payload.message;
    },
    getWalletNetworks: state => {
      state.walletNetworks = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getWalletNetworksSuccess: (state, action) => {
      state.walletNetworks.loading = false;
      state.walletNetworks.data = action.payload.data;
      state.walletNetworks.success = true;
    },
    getWalletNetworksFailed: (state, action) => {
      state.walletNetworks.loading = false;
      state.walletNetworks.error = true;
      state.walletNetworks.message = action.payload.message;
    },
    getWalletCryptos: state => {
      state.walletCryptos = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getWalletCryptosSuccess: (state, action) => {
      state.walletCryptos.loading = false;
      state.walletCryptos.data = action.payload.data;
      state.walletCryptos.success = true;
    },
    getWalletCryptosFailed: (state, action) => {
      state.walletCryptos.loading = false;
      state.walletCryptos.error = true;
      state.walletCryptos.message = action.payload.message;
    },
    // Bank Accounts
    getBankAccounts: state => {
      state.bankAccounts = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
        pagination: null,
      };
    },
    getBankAccountsSuccess: (state, action) => {
      state.bankAccounts.loading = false;
      state.bankAccounts.data = action.payload.data.content;
      state.bankAccounts.pagination = {
        page: action.payload.data.page,
        last_page: action.payload.data.last_page,
        per_page: action.payload.data.per_page,
        total: action.payload.data.total,
      };
      state.bankAccounts.success = true;
    },
    getBankAccountsFailed: (state, action) => {
      state.bankAccounts.loading = false;
      state.bankAccounts.error = true;
      state.bankAccounts.message = action.payload.message;
    },
    // Bank Countries
    getBankCountries: state => {
      state.bankCountries = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getBankCountriesSuccess: (state, action) => {
      state.bankCountries.loading = false;
      state.bankCountries.data = action.payload.data;
      state.bankCountries.success = true;
    },
    getBankCountriesFailed: (state, action) => {
      state.bankCountries.loading = false;
      state.bankCountries.error = true;
      state.bankCountries.message = action.payload.message;
    },
    // Bank Names
    getBankNames: state => {
      state.bankNames = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getBankNamesSuccess: (state, action) => {
      state.bankNames.loading = false;
      state.bankNames.data = action.payload.data;
      state.bankNames.success = true;
    },
    getBankNamesFailed: (state, action) => {
      state.bankNames.loading = false;
      state.bankNames.error = true;
      state.bankNames.message = action.payload.message;
    },
    // Bank Currencies
    getBankCurrencies: state => {
      state.bankCurrencies = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getBankCurrenciesSuccess: (state, action) => {
      state.bankCurrencies.loading = false;
      state.bankCurrencies.data = action.payload.data;
      state.bankCurrencies.success = true;
    },
    getBankCurrenciesFailed: (state, action) => {
      state.bankCurrencies.loading = false;
      state.bankCurrencies.error = true;
      state.bankCurrencies.message = action.payload.message;
    },
    // Currency
    getCurrency: state => {
      state.currency = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getCurrencySuccess: (state, action) => {
      state.currency.loading = false;
      state.currency.data = action.payload.data.content;
      state.currency.success = true;
    },
    getCurrencyFailed: (state, action) => {
      state.currency.loading = false;
      state.currency.error = true;
      state.currency.message = action.payload.message;
    },
    // Bank Create Account (Bank Names)
    getBankCreateAccount: state => {
      state.bankCreateAccount = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getBankCreateAccountSuccess: (state, action) => {
      state.bankCreateAccount.loading = false;
      state.bankCreateAccount.data = action.payload.data;
      state.bankCreateAccount.success = true;
    },
    getBankCreateAccountFailed: (state, action) => {
      state.bankCreateAccount.loading = false;
      state.bankCreateAccount.error = true;
      state.bankCreateAccount.message = action.payload.message;
    },
    // Google 2FA
    getGoogle2FA: state => {
      state.google2FA = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getGoogle2FASuccess: (state, action) => {
      state.google2FA.loading = false;
      state.google2FA.data = action.payload.data;
      state.google2FA.success = true;
    },
    getGoogle2FAFailed: (state, action) => {
      state.google2FA.loading = false;
      state.google2FA.error = true;
      state.google2FA.message = action.payload.message;
    },
  },
});

export const {
  getLegalDocuments,
  getLegalDocumentsSuccess,
  getLegalDocumentsFailed,
  getProfile,
  getProfileSuccess,
  getProfileFailed,
  getWallets,
  getWalletsSuccess,
  getWalletsFailed,
  createWallet,
  createWalletSuccess,
  createWalletFailed,
  updateWallet,
  updateWalletSuccess,
  updateWalletFailed,
  getWalletSystems,
  getWalletSystemsSuccess,
  getWalletSystemsFailed,
  getWalletNetworks,
  getWalletNetworksSuccess,
  getWalletNetworksFailed,
  getWalletCryptos,
  getWalletCryptosSuccess,
  getWalletCryptosFailed,
  getBankAccounts,
  getBankAccountsSuccess,
  getBankAccountsFailed,
  getBankCountries,
  getBankCountriesSuccess,
  getBankCountriesFailed,
  getBankNames,
  getBankNamesSuccess,
  getBankNamesFailed,
  getBankCurrencies,
  getBankCurrenciesSuccess,
  getBankCurrenciesFailed,
  getCurrency,
  getCurrencySuccess,
  getCurrencyFailed,
  getBankCreateAccount,
  getBankCreateAccountSuccess,
  getBankCreateAccountFailed,
  getGoogle2FA,
  getGoogle2FASuccess,
  getGoogle2FAFailed,
} = profileSlice.actions;

export default profileSlice.reducer;
