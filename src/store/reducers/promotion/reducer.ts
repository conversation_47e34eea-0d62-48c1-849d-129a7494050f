import {createSlice} from '@reduxjs/toolkit';

interface PromotionState {
  getPromotion: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
  };
}

const initialState = {
  getPromotion: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
  },
};

const promotionSlice = createSlice({
  name: 'promotion',
  initialState,
  reducers: {
    getPromotion: (state: PromotionState, action) => {
      state.getPromotion.loading = true;
    },
    getPromotionSuccess: (state: PromotionState, action) => {
      state.getPromotion.loading = false;
      if (action.payload.data.page === 1) {
        state.getPromotion.data = action.payload.data.content;
      } else if (action.payload.data.page > 1)
        state.getPromotion.data = [
          ...state.getPromotion.data,
          ...action.payload.data.content,
        ];
      state.getPromotion.success = true;
      state.getPromotion.page = action.payload?.data?.page;
      state.getPromotion.last_page = action.payload?.data?.last_page;
    },
    getPromotionFailed: (state: PromotionState, action) => {
      state.getPromotion.loading = false;
      state.getPromotion.error = true;
      state.getPromotion.message = action.payload.message;
    },
    getPromotionUpdatePage: (state: PromotionState, action) => {
      state.getPromotion.page = action.payload;
    },
    resetPromotion: (state: PromotionState) => {
      state.getPromotion = initialState.getPromotion;
    },
  },
});

export const {
  getPromotion,
  getPromotionSuccess,
  getPromotionFailed,
  getPromotionUpdatePage,
  resetPromotion,
} = promotionSlice.actions;
export default promotionSlice.reducer;
