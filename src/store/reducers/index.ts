import { combineReducers } from 'redux';
import userReducer from './user/user';
import screenFocusReducer from './screenFocus/reducer';
import profileReducer from './profile/profile';
import contactReducer from './contact/reducer';
import promotionReducer from './promotion/reducer';
import announcementReducer from './announcement/reducer';
import transactionReducer from './transaction/reducer';
import referReducer from './refer/reducer';
import accountReducer from './account/reducer'

const rootReducer = combineReducers({
  user: userReducer,
  screenFocus: screenFocusReducer,
  profile: profileReducer,
  contact: contactReducer,
  promotion: promotionReducer,
  announcement: announcementReducer,
  transaction: transactionReducer,
  refer: referReducer,
  account: accountReducer
});

export default rootReducer;
