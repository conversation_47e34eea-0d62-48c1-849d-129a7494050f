import {createSlice} from '@reduxjs/toolkit';

interface TransactionState {
  getTransaction: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
  };
  getDeposit: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
  };
  getWithdraw: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
  };
  getTransfer: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
  };
  getDepositMethod: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
  };
  getWithdrawMethod: {
    loading: boolean;
    data: any[];
    error: boolean | null;
    message: string;
    success: boolean | null;
    page: number;
    last_page: number;
  };
  getMethodDetail: {
    loading: boolean;
    data: any;
    error: boolean | null;
    message: string;
    success: boolean | null;
  };
}

const initialState = {
  getTransaction: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
  },
  getDeposit: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
  },
  getWithdraw: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
  },
  getTransfer: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
  },
  getDepositMethod: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
  },
  getWithdrawMethod: {
    loading: false,
    data: [],
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
    page: 1,
    last_page: 1,
  },
  getMethodDetail: {
    loading: false,
    data: {},
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
};

const transactionSlice = createSlice({
  name: 'transaction',
  initialState,
  reducers: {
    getTransaction: (state: TransactionState, action) => {
      state.getTransaction.loading = true;
    },
    getTransactionSuccess: (state: TransactionState, action) => {
      state.getTransaction.loading = false;
      if (action.payload.data.page === 1) {
        state.getTransaction.data = action.payload.data.content;
      } else if (action.payload.data.page > 1)
        state.getTransaction.data = [
          ...state.getTransaction.data,
          ...action.payload.data.content,
        ];
      state.getTransaction.success = true;
      state.getTransaction.page = action.payload?.data?.page;
      state.getTransaction.last_page = action.payload?.data?.last_page;
    },
    getTransactionFailed: (state: TransactionState, action) => {
      state.getTransaction.loading = false;
      state.getTransaction.error = true;
      state.getTransaction.message = action.payload.message;
    },
    getTransactionUpdatePage: (state: TransactionState, action) => {
      state.getTransaction.page = action.payload;
    },
    resetTransaction: (state: TransactionState) => {
      state.getTransaction = initialState.getTransaction;
    },
    getDeposit: (state: TransactionState, action) => {
      state.getDeposit.loading = true;
    },
    getDepositSuccess: (state: TransactionState, action) => {
      state.getDeposit.loading = false;
      if (action.payload.data.page === 1) {
        state.getDeposit.data = action.payload.data.content;
      } else if (action.payload.data.page > 1)
        state.getDeposit.data = [
          ...state.getDeposit.data,
          ...action.payload.data.content,
        ];
      state.getDeposit.success = true;
      state.getDeposit.page = action.payload?.data?.page;
      state.getDeposit.last_page = action.payload?.data?.last_page;
    },
    getDepositFailed: (state: TransactionState, action) => {
      state.getDeposit.loading = false;
      state.getDeposit.error = true;
      state.getDeposit.message = action.payload.message;
    },
    getDepositUpdatePage: (state: TransactionState, action) => {
      state.getDeposit.page = action.payload;
    },
    resetDeposit: (state: TransactionState) => {
      state.getDeposit = initialState.getDeposit;
    },
    getWithdraw: (state: TransactionState, action) => {
      state.getWithdraw.loading = true;
    },
    getWithdrawSuccess: (state: TransactionState, action) => {
      state.getWithdraw.loading = false;
      if (action.payload.data.page === 1) {
        state.getWithdraw.data = action.payload.data.content;
      } else if (action.payload.data.page > 1)
        state.getWithdraw.data = [
          ...state.getWithdraw.data,
          ...action.payload.data.content,
        ];
      state.getWithdraw.success = true;
      state.getWithdraw.page = action.payload?.data?.page;
      state.getWithdraw.last_page = action.payload?.data?.last_page;
    },
    getWithdrawFailed: (state: TransactionState, action) => {
      state.getWithdraw.loading = false;
      state.getWithdraw.error = true;
      state.getWithdraw.message = action.payload.message;
    },
    getWithdrawUpdatePage: (state: TransactionState, action) => {
      state.getWithdraw.page = action.payload;
    },
    resetWithdraw: (state: TransactionState) => {
      state.getWithdraw = initialState.getWithdraw;
    },
    getTransfer: (state: TransactionState, action) => {
      state.getTransfer.loading = true;
    },
    getTransferSuccess: (state: TransactionState, action) => {
      state.getTransfer.loading = false;
      if (action.payload.data.page === 1) {
        state.getTransfer.data = action.payload.data.content;
      } else if (action.payload.data.page > 1)
        state.getTransfer.data = [
          ...state.getTransfer.data,
          ...action.payload.data.content,
        ];
      state.getTransfer.success = true;
      state.getTransfer.page = action.payload?.data?.page;
      state.getTransfer.last_page = action.payload?.data?.last_page;
    },
    getTransferFailed: (state: TransactionState, action) => {
      state.getTransfer.loading = false;
      state.getTransfer.error = true;
      state.getTransfer.message = action.payload.message;
    },
    getTransferUpdatePage: (state: TransactionState, action) => {
      state.getTransfer.page = action.payload;
    },
    resetTransfer: (state: TransactionState) => {
      state.getTransfer = initialState.getTransfer;
    },
    getDepositMethod: (state: TransactionState, action) => {
      state.getDepositMethod.loading = true;
    },
    getDepositMethodSuccess: (state: TransactionState, action) => {
      state.getDepositMethod.loading = false;
      if (action.payload.data.page === 1) {
        state.getDepositMethod.data = action.payload.data.content;
      } else if (action.payload.data.page > 1)
        state.getDepositMethod.data = [
          ...state.getDeposit.data,
          ...action.payload.data.content,
        ];
      state.getDepositMethod.success = true;
      state.getDepositMethod.page = action.payload?.data?.page;
      state.getDepositMethod.last_page = action.payload?.data?.last_page;
    },
    getDepositMethodFailed: (state: TransactionState, action) => {
      state.getDepositMethod.loading = false;
      state.getDepositMethod.error = true;
      state.getDepositMethod.message = action.payload.message;
    },
    resetDepositMethod: (state: TransactionState) => {
      state.getDepositMethod = initialState.getDepositMethod;
    },
    getWithdrawMethod: (state: TransactionState, action) => {
      state.getWithdrawMethod.loading = true;
    },
    getWithdrawMethodSuccess: (state: TransactionState, action) => {
      state.getWithdrawMethod.loading = false;
      if (action.payload.data.page === 1) {
        state.getWithdrawMethod.data = action.payload.data.content;
      } else if (action.payload.data.page > 1)
        state.getWithdrawMethod.data = [
          ...state.getDeposit.data,
          ...action.payload.data.content,
        ];
      state.getWithdrawMethod.success = true;
      state.getWithdrawMethod.page = action.payload?.data?.page;
      state.getWithdrawMethod.last_page = action.payload?.data?.last_page;
    },
    getWithdrawMethodFailed: (state: TransactionState, action) => {
      state.getWithdrawMethod.loading = false;
      state.getWithdrawMethod.error = true;
      state.getWithdrawMethod.message = action.payload.message;
    },
    resetWithdrawMethod: (state: TransactionState) => {
      state.getWithdrawMethod = initialState.getWithdrawMethod;
    },
    getMethodDetail: (state: TransactionState, action) => {
      state.getMethodDetail.loading = true;
    },
    getMethodDetailSuccess: (state: TransactionState, action) => {
      state.getMethodDetail.loading = false;
      state.getMethodDetail.data = action?.payload?.data;
      state.getMethodDetail.success = true;
    },
    getMethodDetailFailed: (state: TransactionState, action) => {
      state.getMethodDetail.loading = false;
      state.getMethodDetail.error = true;
      state.getMethodDetail.message = action.payload.message;
    },
  },
});

export const {
  getTransaction,
  getTransactionSuccess,
  getTransactionFailed,
  getTransactionUpdatePage,
  resetTransaction,
  getDeposit,
  getDepositSuccess,
  getDepositFailed,
  getDepositUpdatePage,
  resetDeposit,
  getWithdraw,
  getWithdrawSuccess,
  getWithdrawFailed,
  getWithdrawUpdatePage,
  resetWithdraw,
  getTransfer,
  getTransferSuccess,
  getTransferFailed,
  getTransferUpdatePage,
  resetTransfer,
  getDepositMethod,
  getDepositMethodSuccess,
  getDepositMethodFailed,
  resetDepositMethod,
  getWithdrawMethod,
  getWithdrawMethodSuccess,
  getWithdrawMethodFailed,
} = transactionSlice.actions;
export default transactionSlice.reducer;
