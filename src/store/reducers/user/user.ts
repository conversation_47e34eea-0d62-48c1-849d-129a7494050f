import { createSlice } from '@reduxjs/toolkit';
const initialState = {
  getRegister: {
    loading: false,
    data: null,
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
  login: {
    loading: false,
    data: null,
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
  country: {
    loading: false,
    data: null,
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
  forgotUsername: {
    loading: false,
    data: null,
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
  totalBalance: {
    loading: false,
    data: null,
    error: null as boolean | null,
    message: '',
    success: null as boolean | null,
  },
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    getRegister: state => {
      state.getRegister = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getRegisterSuccess: (state, action) => {
      state.getRegister.loading = false;
      state.getRegister.data = action.payload.data;
      state.getRegister.success = true;
    },
    getRegisterFailed: (state, action) => {
      state.getRegister.loading = false;
      state.getRegister.error = true;
      state.getRegister.message = action.payload.message;
    },
    login: state => {
      state.login = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    loginSuccess: (state, action) => {
      state.login.loading = false;
      state.login.data = action.payload.data;
      state.login.success = true;
    },
    loginFailed: (state, action) => {
      console.log('Error in loginFailed action:', action.payload);
      state.login.loading = false;
      state.login.error = true;
      state.login.message = action.payload.message;
    },
    getCountry: state => {
      state.country = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getCountrySuccess: (state, action) => {
      state.country.loading = false;
      state.country.data = action.payload.data;
      state.country.success = true;
    },
    getCountryFailed: (state, action) => {
      state.country.loading = false;
      state.country.error = true;
      state.country.message = action.payload.message;
    },
    forgotUsername: state => {
      state.forgotUsername = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    forgotUsernameSuccess: (state, action) => {
      state.forgotUsername.loading = false;
      state.forgotUsername.data = action.payload.data;
      state.forgotUsername.success = true;
    },
    forgotUsernameFailed: (state, action) => {
      state.forgotUsername.loading = false;
      state.forgotUsername.error = true;
      state.forgotUsername.message = action.payload.message;
    },
    getTotalBalance: state => {
      state.totalBalance = {
        loading: true,
        data: null,
        error: null,
        message: '',
        success: null,
      };
    },
    getTotalBalanceSuccess: (state, action) => {
      state.totalBalance.loading = false;
      state.totalBalance.data = action.payload.data;
      state.totalBalance.success = true;
    },
    getTotalBalanceFailed: (state, action) => {
      state.totalBalance.loading = false;
      state.totalBalance.error = true;
      state.totalBalance.message = action.payload.message;
    },
  },
});

export const {
  getRegister,
  getRegisterSuccess,
  getRegisterFailed,
  login,
  loginSuccess,
  loginFailed,
  getCountry,
  getCountrySuccess,
  getCountryFailed,
  forgotUsername,
  forgotUsernameSuccess,
  forgotUsernameFailed,
  getTotalBalance,
  getTotalBalanceSuccess,
  getTotalBalanceFailed,
} = userSlice.actions;
export default userSlice.reducer;
