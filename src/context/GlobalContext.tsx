import React, {createContext, useContext, useEffect, useState} from 'react';
import Toast from 'react-native-toast-message';

import {BottomSheetModalProvider} from '@gorhom/bottom-sheet';

interface ModalProps {
  message: string;
  type: 'success' | 'info' | 'error' | 'custom' | 'message';
  onSubmit?: () => void;
  onCancel?: () => void;
}

interface ToastProps {
  message: string;
  type: 'success' | 'info' | 'error' | 'custom' | 'message';
  position: 'top' | 'bottom';
}

interface GlobalContextType {
  isLoading: boolean;
  handleLoading: (state: boolean) => void;
  isModalVisible: boolean;
  handleModal: (state: boolean) => void;
  modalProps: ModalProps;
  setModalProps: (props: ModalProps) => void;
  showMessage: (props: ModalProps) => void;
  showToast: (props: ToastProps) => void;
}

const defaultModalProps: ModalProps = {
  message: '',
  type: 'info',
};

const GlobalContext = createContext<GlobalContextType>({
  isLoading: false,
  handleLoading: () => {},
  isModalVisible: false,
  handleModal: () => {},
  modalProps: defaultModalProps,
  setModalProps: () => {},
  showMessage: () => {},
  showToast: () => {},
});

export const GlobalContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalProps, setModalProps] = useState<ModalProps>(defaultModalProps);

  const handleLoading = (state: boolean) => setIsLoading(state);
  const handleModal = (state: boolean) => setIsModalVisible(state);

  const showMessage = (props: ModalProps) => {
    setModalProps(props);
    handleModal(true);
  };

  const showToast = (props: ToastProps) => {
    Toast.show({
      type: props.type,
      text2: props.message,
      position: props.position,
    });
  };

  useEffect(() => {
    console.log('GlobalContext mounted');
    Toast.show({
      type: 'info',
      text1: 'This is an info message',
      position: 'bottom',
    });
  }, []);

  return (
    <BottomSheetModalProvider>
      <GlobalContext.Provider
        value={{
          isLoading,
          handleLoading,
          isModalVisible,
          handleModal,
          modalProps,
          setModalProps,
          showMessage,
          showToast,
        }}>
        {children}
      </GlobalContext.Provider>
    </BottomSheetModalProvider>
  );
};

export const useGlobalContext = () => useContext(GlobalContext);
