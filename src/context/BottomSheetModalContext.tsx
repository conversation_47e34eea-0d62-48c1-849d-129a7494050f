import {
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import {colors, fonts, images} from 'assets';
import React, {
  createContext,
  useCallback,
  useContext,
  useRef,
  useState,
} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity} from 'react-native';
import {scale} from 'utils/device';

interface BottomSheetProps {
  children: React.ReactNode;
  enablePanDownToClose?: boolean;
  showCloseBtn?: boolean;
  showBackBtn?: boolean;
  onPressBack?: () => void; // Optional callback for back button
  onPressClose?: () => void; // Optional callback for close button
}

interface BottomSheetContextType {
  openBottomSheet: (props: BottomSheetProps) => void;
  closeBottomSheet: () => void;
}

const BottomSheetContext = createContext<BottomSheetContextType | undefined>(
  undefined,
);

export const BottomSheetProvider = ({
  children,
  enablePanDownToClose,
}: BottomSheetProps) => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const [bottomSheetContent, setBottomSheetContent] =
    useState<React.ReactNode>(null);
  const [showCloseBtn, setShowCloseBtn] = useState<boolean | undefined>(false);
  const [showBackBtn, setShowBackBtn] = useState<boolean | undefined>(false);
  const [onPressBack, setOnPressBack] = useState<(() => void) | undefined>(
    undefined,
  );
  const [onPressClose, setOnPressClose] = useState<(() => void) | undefined>(
    undefined,
  );
  const openBottomSheet = useCallback(
    ({
      children,
      showCloseBtn,
      showBackBtn,
      onPressBack,
      onPressClose,
    }: BottomSheetProps) => {
      setBottomSheetContent(children);
      setShowCloseBtn(showCloseBtn);
      setShowBackBtn(showBackBtn);
      bottomSheetRef.current?.present();
      setOnPressBack(() => onPressBack);
      setOnPressClose(() => onPressClose);
    },
    [],
  );

  const closeBottomSheet = useCallback(() => {
    bottomSheetRef.current?.dismiss();
    setBottomSheetContent(null);
  }, []);

  return (
    <BottomSheetModalProvider>
      <BottomSheetContext.Provider value={{openBottomSheet, closeBottomSheet}}>
        {children}

        {/* Bottom Sheet */}
        <BottomSheetModal
          ref={bottomSheetRef}
          snapPoints={[815]}
          enablePanDownToClose={enablePanDownToClose}
          handleComponent={() => <></>}
          backgroundStyle={styles.background}>
          <BottomSheetView style={styles.contentContainer}>
            {showBackBtn && (
              <TouchableOpacity
                onPress={onPressBack ? onPressBack : closeBottomSheet}
                style={styles.backButton}>
                <Image source={images.ic_back} style={styles.iconBack} />
                <Text style={styles.back}>Back</Text>
              </TouchableOpacity>
            )}
            {showCloseBtn && (
              <TouchableOpacity
                activeOpacity={1}
                style={styles.closeButton}
                onPress={onPressClose ? onPressClose : closeBottomSheet}>
                <Image source={images.ic_close} style={styles.icClose} />
              </TouchableOpacity>
            )}
            {bottomSheetContent || <Text>No Content</Text>}
          </BottomSheetView>
        </BottomSheetModal>
      </BottomSheetContext.Provider>
    </BottomSheetModalProvider>
  );
};

export const useBottomSheet = () => {
  const context = useContext(BottomSheetContext);
  if (!context) {
    throw new Error('useBottomSheet must be used within a BottomSheetProvider');
  }
  return context;
};

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    // alignItems: 'center',
    // justifyContent: 'center',
    backgroundColor: colors.black,
    borderTopLeftRadius: scale(20),
    borderTopRightRadius: scale(20),
  },
  background: {
    backgroundColor: colors.black,
    borderTopLeftRadius: scale(20),
    borderTopRightRadius: scale(20),
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    left: scale(20),
    top: scale(20),
  },
  iconBack: {
    width: scale(24),
    height: scale(24),
  },
  back: {
    fontFamily: fonts.SFPro.medium,
    fontSize: scale(17),
    color: colors.white,
    marginLeft: scale(10),
    zIndex: 99999,
  },
  icClose: {
    width: scale(16),
    height: scale(16),
  },
  closeButton: {
    padding: scale(10),
    width: scale(36),
    height: scale(36),
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    right: scale(20),
    top: scale(20),
    zIndex: 99999,
  },
});
