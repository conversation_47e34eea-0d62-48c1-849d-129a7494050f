import network from './network';
import {API_URL} from './constants';
import qs from 'qs';
import {Alert} from 'react-native';
import {navigationRef} from 'navigation/AppNavigation';
import {CommonActions} from '@react-navigation/native';

const handleRequest = async (
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  data?: any,
  isAuthorized: boolean = false,
  isFormData: boolean = false,
) => {
  try {
    const requestFn = isAuthorized
      ? isFormData
        ? network.authorizedRequestFormData
        : network.authorizedRequest
      : network.unAuthorizedRequest;

    const response: any = await requestFn(endpoint, method, data);
    if (!response || response.status >= 400) {
      if (response?.status === 401) {
        navigationRef.dispatch(
          CommonActions.reset({routes: [{name: 'AuthenticationStack'}]}),
        );
        Alert.alert('Message', 'Session expired. Plese login again', [
          {
            text: 'OK',
            onPress: () => {},
          },
        ]);
      }
      throw response;
    }

    return response;
  } catch (error: any) {
    console.error(`API Error [${method}] ${endpoint}:`, error.data);
    throw error;
  }
};

export default {
  register: (data: any) => handleRequest(API_URL.REGISTER, 'POST', data),
  login: (data: any) => handleRequest(API_URL.LOGIN, 'POST', data),
  getCountry: () => handleRequest(API_URL.COUNTRY, 'GET'),
  forgotUsername: (data: any) =>
    handleRequest(API_URL.FORGOT_USERNAME, 'POST', data),
  getLegalDocuments: () =>
    handleRequest(API_URL.LEGAL_DOCUMENTS, 'GET', null, true),
  getProfile: () => handleRequest(API_URL.PROFILE, 'GET', null, true),
  getTotalBalance: () =>
    handleRequest(
      `${API_URL.TOTAL_BALANCE}?excludeCent=true`,
      'GET',
      null,
      true,
    ),
  sendContact: (data: any) =>
    handleRequest(API_URL.CONTACT, 'POST', data, true),
  getPromotion: (data: any) =>
    handleRequest(`${API_URL.PROMOTION}?page=${data?.page}`, 'GET', null, true),
  getAnnouncement: (data: any) =>
    handleRequest(
      `${API_URL.ANNOUNCEMENT}?page=${data?.page}`,
      'GET',
      null,
      true,
    ),
  getTransaction: (data: any) => {
    if (data?.type) {
      return handleRequest(
        `${API_URL.TRANSACTION_HISTORY}&page=${data?.page}&type=${data?.type}`,
        'GET',
        null,
        true,
      );
    } else {
      return handleRequest(
        `${API_URL.TRANSACTION_HISTORY}&page=${data?.page}`,
        'GET',
        null,
        true,
      );
    }
  },
  getMethod: (data: any) =>
    handleRequest(
      `${API_URL.TRANSFER_METHOD}?type=${data?.type}`,
      'GET',
      null,
      true,
    ),
  getMethodDetail: (data: any) =>
    handleRequest(
      `${API_URL.TRANSFER_METHOD}/${data?.identifier}`,
      'GET',
      null,
      true,
    ),
  getMyIBClients: (data: any) => {
    const params = new URLSearchParams();
    params.append('include', 'user,group,balance');
    params.append('orderBy', 'created_at');
    params.append('sortedBy', 'DESC');
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    if (data?.keyword) {
      params.append('keyword', data.keyword);
      params.append('searchFields', 'login:like');
    }
    return handleRequest(
      `${API_URL.MY_IB_CLIENTS}?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  getMIBClients: (data: any) => {
    const params = new URLSearchParams();
    params.append('include', 'user,group,balance');
    params.append('orderBy', 'created_at');
    params.append('sortedBy', 'DESC');
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    if (data?.keyword) {
      params.append('keyword', data.keyword);
      params.append('searchFields', 'login:like');
    }
    return handleRequest(
      `${API_URL.MIB_CLIENTS}?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  getMyIBAndMIB: (data: any) => {
    const params = new URLSearchParams();
    params.append('include', 'group,balance');
    params.append('orderBy', 'created_at');
    params.append('sortedBy', 'DESC');
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    if (data?.keyword) {
      params.append('keyword', data.keyword);
      params.append('searchFields', 'login:like');
    }
    return handleRequest(
      `${API_URL.MY_IB_AND_MIB}?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  getIBsUnder: (data: any) => {
    const params = new URLSearchParams();
    params.append('include', 'user,group,account');
    params.append('orderBy', 'created_at');
    params.append('sortedBy', 'DESC');
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    if (data?.keyword) {
      params.append('keyword', data.keyword);
      params.append('searchFields', 'login:like');
    }
    return handleRequest(
      `${API_URL.IBS_UNDER}?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  getIBRequest: (data: any) => {
    const params = new URLSearchParams();
    params.append('orderBy', 'created_at');
    params.append('sortedBy', 'DESC');
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    if (data?.keyword) {
      params.append('keyword', data.keyword);
      params.append('searchFields', 'campaign_name:like');
    }
    return handleRequest(
      `${API_URL.IB_REQUEST}?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  createIBRequest: (data: any) => {
    return handleRequest(API_URL.CREATE_IB_REQUEST, 'POST', data, true);
  },
  getTradeHistory: (data: any) => {
    const params = new URLSearchParams();
    params.append('orderBy', 'Time');
    params.append('sortedBy', 'DESC');
    params.append('include', 'dealin');
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    return handleRequest(
      `${API_URL.TRADE_HISTORY}/${
        data.accountId
      }/tradehistory?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  getTotalProfit: (data: any) => {
    const params = new URLSearchParams();
    params.append('orderBy', 'Time');
    params.append('sortedBy', 'DESC');
    params.append('page', '1');
    params.append('limit', '10');
    params.append('include', 'dealin');
    return handleRequest(
      `${API_URL.TOTAL_PROFIT}/${
        data.accountId
      }/total-profit?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  getTotalVolume: (data: any) => {
    const params = new URLSearchParams();
    params.append('orderBy', 'Time');
    params.append('sortedBy', 'DESC');
    params.append('page', '1');
    params.append('limit', '10');
    params.append('include', 'dealin');
    return handleRequest(
      `${API_URL.TOTAL_VOLUME}/${
        data.accountId
      }/total-volume?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  getCommissionHistory: (data: any) => {
    const params = new URLSearchParams();
    if (data?.orderBy) {
      params.append('orderBy', data.orderBy);
    }
    if (data?.sortedBy) {
      params.append('sortedBy', data.sortedBy);
    }
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    if (data?.include) {
      params.append('include', data.include);
    }
    return handleRequest(
      `${API_URL.COMMISSION_HISTORY}/${
        data.accountId
      }/commission?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  getTotalCommission: (data: any) => {
    const params = new URLSearchParams();
    if (data?.orderBy) {
      params.append('orderBy', data.orderBy);
    }
    if (data?.sortedBy) {
      params.append('sortedBy', data.sortedBy);
    }
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    if (data?.date) {
      params.append('date', data.date);
    }
    return handleRequest(
      `${API_URL.TOTAL_COMMISSION}/${
        data.accountId
      }/total-commission?${params.toString()}`,
      'GET',
      null,
      true,
    );
  },
  getAccount: (data: any) =>
    handleRequest(
      `${API_URL.ACCOUNT}?${qs.stringify(data)}`,
      'GET',
      null,
      true,
    ),
  getWallets: (data?: any) => {
    const params = new URLSearchParams();
    if (data?.network) {
      params.append('network', data.network);
    }
    if (data?.crypto) {
      params.append('crypto', data.crypto);
    }
    if (data?.system) {
      params.append('system', data.system);
    }
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    const queryString = params.toString();
    return handleRequest(
      `${API_URL.WALLETS}${queryString ? `?${queryString}` : ''}`,
      'GET',
      null,
      true,
    );
  },
  getWalletSystems: () =>
    handleRequest(API_URL.WALLET_SYSTEMS, 'GET', null, true),
  getWalletNetworks: () =>
    handleRequest(API_URL.WALLET_NETWORKS, 'GET', null, true),
  getWalletCryptos: () =>
    handleRequest(API_URL.WALLET_CRYPTOS, 'GET', null, true),
  getBankAccounts: (data?: any) => {
    const params = new URLSearchParams();
    if (data?.country) {
      params.append('country', data.country);
    }
    if (data?.bank_name) {
      params.append('bank_name', data.bank_name);
    }
    if (data?.currency) {
      params.append('currency', data.currency);
    }
    if (data?.page) {
      params.append('page', data.page);
    }
    if (data?.limit) {
      params.append('limit', data.limit);
    }
    const queryString = params.toString();
    return handleRequest(
      `${API_URL.BANK_ACCOUNTS}${queryString ? `?${queryString}` : ''}`,
      'GET',
      null,
      true,
    );
  },
  getBankCountries: () =>
    handleRequest(API_URL.BANK_COUNTRIES, 'GET', null, true),
  getBankNames: () => handleRequest(API_URL.BANK_NAMES, 'GET', null, true),
  getBankCurrencies: () =>
    handleRequest(API_URL.BANK_CURRENCIES, 'GET', null, true),
};
