import axios, {AxiosPromise} from 'axios';
import {ROOT_HTTP} from './constants';
const BASE_URL = ROOT_HTTP;

type RequestMethod = 'POST' | 'GET' | 'PUT' | 'DELETE';

class Network {
  private static instance = new Network();
  private token: string = '';
  private isNetworkConnectionError: boolean = false;
  private isOtherNetworkError: boolean = false;

  constructor() {
    if (Network.instance) {
      throw new Error(
        'Error: Instantiation failed: Use Network.getInstance() instead of new.',
      );
    }
    Network.instance = this;
  }

  public static getInstance(): Network {
    return Network.instance;
  }

  setToken(token: string) {
    this.token = token;
  }

  getIsNetworkConnectionError() {
    return this.isNetworkConnectionError;
  }

  setIsNetworkConnectionError(isNetworkConnectionError: boolean) {
    this.isNetworkConnectionError = isNetworkConnectionError;
  }

  getIsOtherNetworkError() {
    return this.isOtherNetworkError;
  }

  setIsOtherNetworkError(isOtherNetworkError: boolean) {
    this.isOtherNetworkError = isOtherNetworkError;
  }

  unAuthorizedRequest<T>(
    url: string,
    method: RequestMethod = 'GET',
    data?: any,
    // params?: object,
    header?: object,
    baseURL?: string,
  ): AxiosPromise<T> {
    const response: AxiosPromise<T> = axios({
      method: method,
      url: url,
      baseURL: baseURL ? baseURL : BASE_URL,
      data: data ? data : undefined,
      // params: params ? params : null,
      timeout: 30000,
      headers: {
        ...header,
        'Content-Type': 'application/json',
        'Accept-Language': 'en',
      },
    });
    return response;
  }

  authorizedRequest = (
    url: string,
    method: RequestMethod,
    data?: any,
    header?: any,
  ) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response: any = await axios({
          method: method,
          url: url,
          baseURL: BASE_URL,
          data: data ? data : undefined,
          timeout: 30000,
          headers: {
            ...header,
            'Content-Type': 'application/json',
            'Accept-Language': 'en',
            Authorization: 'Bearer ' + this.token,
          },
        });
        if (!response?.error) {
          resolve(response);
        } else if (response?.error) {
          reject(response.data);
        }
      } catch (error) {
        console.log('CHECK NETWORK ERROR', error);
        reject(error);
      }
    });
  };

  authorizedRequestFormData = (
    url: string,
    method: RequestMethod,
    data?: any,
    header?: any,
  ) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response: any = await axios({
          method: method,
          url: url,
          baseURL: BASE_URL,
          data: data ? data : undefined,
          timeout: 30000,
          headers: {
            ...header,
            'Content-Type': 'multipart/form-data',
            'Accept-Language': 'en',
            Authorization: 'Bearer ' + this.token,
          },
        });
        if (!response?.error) {
          resolve(response);
        } else if (response?.error) {
          reject(response.data);
        }
      } catch (error) {
        console.log('CHECK NETWORK ERROR', error);
        reject(error);
      }
    });
  };
}

axios.interceptors.request.use(
  function (config: any) {
    if (__DEV__) {
      const {url, method, data, baseURL} = config;
      const message = {
        METHOD: method,
        HEADER: config.headers,
        DATA: data,
        URL: baseURL + url,
      };
      console.log(
        '%c [HTTP Interceptor Request]',
        'color: blue; font-weight: bold',
        message,
      );
    }

    return config;
  },
  function (error) {
    if (__DEV__) {
      console.log(
        '%c [HTTP Interceptor Request Error]',
        'color: red; font-weight: bold',
        error,
      );
    }
    console.log('error 1: ', JSON.stringify(error));

    return Promise.reject(error);
  },
);

// Add a response interceptor
axios.interceptors.response.use(
  function (response) {
    if (__DEV__) {
      const {data: responseData, config} = response;
      const {url, method} = config;

      const message = {
        METHOD: method,
        DATA: responseData,
        URL: url,
      };
      console.log(
        '%c [HTTP Interceptor Response]',
        'color: #248c1d; font-weight: bold',
        message,
      );
    }
    return response.data;
  },
  function (error) {
    if (__DEV__) {
      const {response} = error;
      const message = {
        METHOD: response.config.method,
        DATA: response.data,
        URL: response.config.url,
      };
      console.log(
        '%c [HTTP Interceptor Response Error]',
        'color: red; font-weight: bold',
        message,
      );
    }
    return error.response;
  },
);

export default Network.getInstance();
