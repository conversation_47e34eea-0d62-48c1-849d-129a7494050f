import messaging from '@react-native-firebase/messaging';
import {Platform} from 'react-native';

import AsyncStorage from '@react-native-async-storage/async-storage';

export const getTokenNotification = async () => {
  try {
    const statusAuthorizationStatus = await messaging().requestPermission();

    if (
      statusAuthorizationStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      statusAuthorizationStatus === messaging.AuthorizationStatus.PROVISIONAL ||
      Platform.OS === 'android'
    ) {
      let tokenNotification = await AsyncStorage.getItem('FCM_TOKEN');

      if (!messaging().isDeviceRegisteredForRemoteMessages) {
        await messaging().registerDeviceForRemoteMessages();
      }
      tokenNotification = await messaging().getToken();
      messaging().onTokenRefresh(async fcmToken => {
        if (fcmToken) {
          tokenNotification = fcmToken;
          await AsyncStorage.setItem('FCM_TOKEN', fcmToken);
        }
      }); //renew token when token is invalid in some cases
      // Get the token
      if (tokenNotification) {
        await AsyncStorage.setItem('FCM_TOKEN', tokenNotification);
      }

      return tokenNotification;
    }
    return null;
  } catch (error) {
    console.log('tokenNotification 39', error);
    return null;
  }
};
