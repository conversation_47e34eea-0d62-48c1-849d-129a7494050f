import notifee, {
  EventType,
  AndroidImportance,
  AndroidVisibility,
} from '@notifee/react-native';
import {getApp} from '@react-native-firebase/app';
import {
  getMessaging,
  onMessage,
  setBackgroundMessageHandler,
} from '@react-native-firebase/messaging';

const messaging = getMessaging(getApp());
class NotificationManager {
  private static instance: NotificationManager;

  private constructor() {
    this.initialize();
  }

  // Singleton để đảm bảo chỉ có một instance
  public static getInstance() {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  // Khởi tạo toàn bộ cấu hình liên quan đến thông báo
  private async initialize() {
    await this.createNotificationChannels();
    this.registerBackgroundHandler();
    this.registerForegroundHandler();
    this.registerEventHandlers();
  }

  // T<PERSON><PERSON> kênh thông báo (Android)
  private async createNotificationChannels() {
    try {
      await notifee.createChannel({
        id: 'default',
        name: 'Default Channel',
        importance: AndroidImportance.HIGH,
        visibility: AndroidVisibility.PUBLIC,
        vibration: true,
      });
      console.log('Notification channels created.');
    } catch (error) {
      console.error('Error creating notification channels:', error);
    }
  }

  // Hiển thị thông báo
  public async showNotification(
    title: string,
    body: string,
    data: Record<string, any> = {},
    androidOptions: Record<string, any> = {},
    iosOptions: Record<string, any> = {},
  ) {
    try {
      await notifee.displayNotification({
        title,
        body,
        data,
        android: {
          channelId: 'default',
          smallIcon: 'ic_noti',
          ...androidOptions,
        },
        ios: {
          critical: true,
          criticalVolume: 1.0,
          ...iosOptions,
        },
      });
    } catch (error) {
      console.error('Error displaying notification:', error);
    }
  }

  // Đăng ký handler cho thông báo background
  private registerBackgroundHandler() {
    setBackgroundMessageHandler(messaging, async remoteMessage => {
      console.log('THÔNG BÁO KHI ĐANG CHẠY NỀN HOẶC TẮT APP', remoteMessage);
      if (remoteMessage.notification) {
        const {title, body} = remoteMessage.notification;
        await this.showNotification(
          title || 'Notification',
          body || '',
          remoteMessage.data || {},
        );
      }
    });
  }

  // Đăng ký handler cho thông báo foreground
  private registerForegroundHandler() {
    onMessage(messaging, async remoteMessage => {
      console.log('THÔNG BÁO KHI ĐANG MỞ APP', remoteMessage);

      if (remoteMessage.notification) {
        const {title, body} = remoteMessage.notification;

        await this.showNotification(
          title || 'Notification',
          body || '',
          remoteMessage.data || {},
        );
      }
    });
  }

  // Đăng ký sự kiện foreground/background
  private registerEventHandlers() {
    notifee.onForegroundEvent(({type, detail}) => {
      switch (type) {
        case EventType.PRESS:
          console.log('User pressed notification:', detail.notification);
          // Xử lý khi nhấn vào thông báo
          break;
        case EventType.DISMISSED:
          console.log('User dismissed notification:', detail.notification);
          break;
      }
    });

    notifee.onBackgroundEvent(async ({type, detail}) => {
      switch (type) {
        case EventType.PRESS:
          console.log(
            'User pressed notification in background:',
            detail.notification,
          );
          break;
      }
    });
  }

  // Cập nhật số badge (iOS/Android)
  public async setBadgeCount(count: number) {
    try {
      await notifee.setBadgeCount(count);
      console.log(`Badge count updated to ${count}`);
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }

  // Xóa badge
  public async clearBadge() {
    try {
      await notifee.setBadgeCount(0);
      console.log('Badge count cleared');
    } catch (error) {
      console.error('Error clearing badge count:', error);
    }
  }
}

export const NotificationManagerInstance = NotificationManager.getInstance();
NotificationManagerInstance;
