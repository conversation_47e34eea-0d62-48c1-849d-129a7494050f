module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        extensions: ['.tsx', '.ts', '.js', '.json'],
        alias: {
          assets: './src/assets',
          store: './src/store',
          components: './src/components',
          api: './src/api',
          utils: './src/utils',
          screens: './src/screens',
          navigation: './src/navigation',
          assets: './src/assets',
        },
      },
    ],
    'react-native-reanimated/plugin',
    '@babel/plugin-transform-export-namespace-from',
  ],
};
